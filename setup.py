"""
Setup script for 3D Plant Growth Simulation
"""
import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def check_gpu():
    """Check GPU compatibility"""
    print("Checking GPU compatibility...")
    
    try:
        import moderngl
        ctx = moderngl.create_context(standalone=True)
        info = ctx.info
        print(f"✓ OpenGL Version: {info['GL_VERSION']}")
        print(f"✓ GPU: {info['GL_RENDERER']}")
        print(f"✓ Vendor: {info['GL_VENDOR']}")
        
        # Check for minimum OpenGL version
        version_str = info['GL_VERSION'].split()[0]
        major, minor = map(int, version_str.split('.')[:2])
        
        if major >= 3 and minor >= 3:
            print("✓ OpenGL 3.3+ supported - Good for RX 580!")
            return True
        else:
            print("✗ OpenGL 3.3+ required")
            return False
            
    except Exception as e:
        print(f"✗ GPU check failed: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    dirs = ['screenshots', 'logs']
    
    for dir_name in dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✓ Created directory: {dir_name}")

def main():
    """Main setup function"""
    print("=== 3D Plant Growth Simulation Setup ===")
    print()
    
    # Install requirements
    if not install_requirements():
        return 1
    
    print()
    
    # Check GPU
    if not check_gpu():
        print("Warning: GPU compatibility issues detected")
        print("The simulation may run slowly or not at all")
    
    print()
    
    # Create directories
    create_directories()
    
    print()
    print("=== Setup Complete ===")
    print("Run the simulation with: python main.py")
    print()
    print("System Requirements Met:")
    print("✓ Python 3.7+")
    print("✓ OpenGL 3.3+ (optimized for RX 580)")
    print("✓ All dependencies installed")
    print()
    print("Features:")
    print("• L-System based procedural growth")
    print("• Realistic plant behaviors (phototropism, gravitropism)")
    print("• 3D rendering with Phong lighting")
    print("• Procedural bark and leaf textures")
    print("• Interactive camera controls")
    print("• Real-time growth simulation")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
