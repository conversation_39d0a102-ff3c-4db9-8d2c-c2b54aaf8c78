"""
Test plant growth without any 3D rendering - console output only
"""
import time
import sys
from plant_system.plant import Plant

def main():
    """Test plant growth in console mode"""
    print("=== Plant Growth Console Test ===")
    print("Testing L-System based plant growth without 3D rendering")
    print()
    
    try:
        # Create plant
        print("Creating plant...")
        plant = Plant(plant_type='realistic_tree', position=[0, 0, 0])
        print(f"✓ Plant created: {plant.get_stats()}")
        print()
        
        # Simulate growth over time
        print("Simulating growth (press Ctrl+C to stop):")
        print("Time | Age  | Iter | Branches | Leaves | Growing")
        print("-" * 50)
        
        start_time = time.time()
        last_print = 0
        
        while True:
            # Update plant (simulate 60 FPS)
            plant.update(1/60)  # 1/60 second per update
            
            current_time = time.time() - start_time
            
            # Print stats every second
            if current_time - last_print >= 1.0:
                stats = plant.get_stats()
                print(f"{current_time:4.0f}s | {stats['age']:4.1f} | {stats['iteration']:4d} | "
                      f"{stats['branches']:8d} | {stats['total_leaves']:6d} | {stats['growing_branches']:7d}")
                last_print = current_time
                
                # Stop after 60 seconds or when growth stops
                if current_time > 60 or stats['growing_branches'] == 0:
                    break
            
            # Small delay to prevent 100% CPU usage
            time.sleep(0.001)
    
    except KeyboardInterrupt:
        print("\nStopped by user")
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("\nFinal plant statistics:")
    final_stats = plant.get_stats()
    for key, value in final_stats.items():
        print(f"  {key}: {value}")
    
    print("\n✓ Growth test completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
