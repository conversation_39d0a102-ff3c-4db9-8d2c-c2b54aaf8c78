"""
Minimal 3D Plant Growth Simulation - focuses on core functionality
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys
import math

from plant_system.plant import Plant

class MinimalPlantSimulation:
    """Minimal plant simulation focusing on core growth mechanics"""
    
    def __init__(self, width=800, height=600):
        self.width = width
        self.height = height
        self.running = True
        
        print("Initializing Minimal Plant Simulation...")
        
        # Initialize Pygame
        pygame.init()
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        pygame.display.gl_set_attribute(pygame.GL_DEPTH_SIZE, 24)
        
        self.screen = pygame.display.set_mode((width, height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption("3D Plant Growth - Minimal Version")
        
        # Create OpenGL context
        self.ctx = mgl.create_context()
        print(f"OpenGL: {self.ctx.info['GL_VERSION']}")
        print(f"GPU: {self.ctx.info['GL_RENDERER']}")
        
        # Enable depth testing
        self.ctx.enable(mgl.DEPTH_TEST)
        
        # Create simple shader
        self.program = self.ctx.program(
            vertex_shader="""
            #version 330 core
            layout (location = 0) in vec3 aPos;
            uniform mat4 mvp;
            void main() {
                gl_Position = mvp * vec4(aPos, 1.0);
            }
            """,
            fragment_shader="""
            #version 330 core
            out vec4 FragColor;
            uniform vec3 color;
            void main() {
                FragColor = vec4(color, 1.0);
            }
            """
        )
        
        # Create plant
        self.plant = Plant(plant_type='simple_tree', position=[0, 0, 0])
        
        # Camera parameters
        self.camera_distance = 3.0
        self.camera_angle_x = 0.0
        self.camera_angle_y = 0.3
        
        # Control state
        self.keys = {}
        self.mouse_buttons = [False, False, False]
        self.paused = False
        self.growth_speed = 1.0
        self.clock = pygame.time.Clock()
        
        print("✓ Minimal simulation initialized!")
    
    def get_view_projection_matrix(self):
        """Calculate view-projection matrix"""
        # Camera position
        cam_x = self.camera_distance * math.sin(self.camera_angle_y) * math.cos(self.camera_angle_x)
        cam_y = self.camera_distance * math.cos(self.camera_angle_y)
        cam_z = self.camera_distance * math.sin(self.camera_angle_y) * math.sin(self.camera_angle_x)
        
        # Simple view matrix (look at origin)
        eye = np.array([cam_x, cam_y, cam_z])
        target = np.array([0, 1, 0])
        up = np.array([0, 1, 0])
        
        # View matrix
        forward = target - eye
        forward = forward / np.linalg.norm(forward)
        right = np.cross(forward, up)
        right = right / np.linalg.norm(right)
        up = np.cross(right, forward)
        
        view = np.eye(4)
        view[0, :3] = right
        view[1, :3] = up
        view[2, :3] = -forward
        view[:3, 3] = -np.array([np.dot(right, eye), np.dot(up, eye), np.dot(forward, eye)])
        
        # Projection matrix
        fov = math.radians(45)
        aspect = self.width / self.height
        near, far = 0.1, 100.0
        
        f = 1.0 / math.tan(fov / 2.0)
        proj = np.zeros((4, 4))
        proj[0, 0] = f / aspect
        proj[1, 1] = f
        proj[2, 2] = (far + near) / (near - far)
        proj[2, 3] = (2 * far * near) / (near - far)
        proj[3, 2] = -1
        
        return (proj @ view).astype(np.float32)
    
    def render_simple_geometry(self, vertices, color):
        """Render simple geometry with basic shader"""
        if len(vertices) == 0:
            return
        
        # Create vertex buffer
        vbo = self.ctx.buffer(vertices.astype(np.float32).tobytes())
        vao = self.ctx.vertex_array(self.program, [(vbo, '3f', 'aPos')])
        
        # Set uniforms
        mvp = self.get_view_projection_matrix()
        self.program['mvp'].write(mvp.tobytes())
        self.program['color'].value = color
        
        # Render
        vao.render()
        
        # Cleanup
        vao.release()
        vbo.release()
    
    def render_plant_simple(self):
        """Render plant with simple line segments"""
        # Get branch geometry
        branch_geom = self.plant.get_branch_geometry()
        if branch_geom[0] is not None:
            vertices, normals, texcoords, indices = branch_geom
            
            # Convert to line segments for simple rendering
            lines = []
            for i in range(0, len(indices), 3):
                if i + 2 < len(indices):
                    # Add triangle edges as lines
                    a, b, c = indices[i], indices[i+1], indices[i+2]
                    lines.extend([vertices[a], vertices[b]])
                    lines.extend([vertices[b], vertices[c]])
                    lines.extend([vertices[c], vertices[a]])
            
            if lines:
                line_vertices = np.array(lines)
                self.render_simple_geometry(line_vertices, (0.6, 0.4, 0.2))  # Brown
        
        # Render leaves as simple points
        leaf_geom = self.plant.get_leaf_geometry()
        if leaf_geom[0] is not None:
            vertices, normals, texcoords, indices = leaf_geom
            # Just render leaf vertices as points
            self.render_simple_geometry(vertices, (0.2, 0.8, 0.2))  # Green
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    print(f"{'Paused' if self.paused else 'Resumed'}")
                elif event.key == pygame.K_r:
                    self.plant.reset()
                    print("Plant reset")
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.growth_speed = min(5.0, self.growth_speed * 1.2)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
                elif event.key == pygame.K_MINUS:
                    self.growth_speed = max(0.1, self.growth_speed / 1.2)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.mouse_buttons[event.button - 1] = True
            
            elif event.type == pygame.MOUSEBUTTONUP:
                self.mouse_buttons[event.button - 1] = False
            
            elif event.type == pygame.MOUSEWHEEL:
                self.camera_distance = max(1.0, min(10.0, self.camera_distance - event.y * 0.5))
            
            elif event.type == pygame.MOUSEMOTION:
                if self.mouse_buttons[0]:  # Left mouse button
                    self.camera_angle_x += event.rel[0] * 0.01
                    self.camera_angle_y = max(0.1, min(math.pi - 0.1, 
                                                     self.camera_angle_y + event.rel[1] * 0.01))
    
    def run(self):
        """Main application loop"""
        print("Starting minimal plant simulation...")
        print("\nControls:")
        print("  Mouse: Rotate camera")
        print("  Mouse wheel: Zoom")
        print("  Space: Pause/Resume")
        print("  R: Reset plant")
        print("  +/-: Growth speed")
        print("  ESC: Exit")
        print()
        
        last_stats_time = time.time()
        
        while self.running:
            dt = self.clock.tick(60) / 1000.0
            
            # Handle events
            self.handle_events()
            
            # Update plant
            if not self.paused:
                self.plant.update(dt * self.growth_speed)
            
            # Render
            self.ctx.clear(0.5, 0.7, 1.0, 1.0)  # Sky blue background
            self.render_plant_simple()
            pygame.display.flip()
            
            # Print stats every 5 seconds
            current_time = time.time()
            if current_time - last_stats_time >= 5.0:
                stats = self.plant.get_stats()
                fps = self.clock.get_fps()
                print(f"Age: {stats['age']:.1f}s, Branches: {stats['branches']}, "
                      f"Iteration: {stats['iteration']}, FPS: {fps:.1f}")
                last_stats_time = current_time
        
        pygame.quit()
        print("Simulation ended")

def main():
    """Main entry point"""
    try:
        simulation = MinimalPlantSimulation(800, 600)
        simulation.run()
        return 0
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
