"""
Test script for plant growth simulation components
"""
import numpy as np
import sys
import traceback

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test standard libraries
        import numpy as np
        import math
        import time
        print("✓ Standard libraries")
        
        # Test plant system
        from plant_system.lsystem import LSystem, create_plant_lsystem
        from plant_system.plant import Plant
        from plant_system.branch import Branch
        from plant_system.leaf import Leaf
        print("✓ Plant system modules")
        
        # Test utilities
        from utils.math_utils import normalize, rotation_matrix_x
        from utils.noise import PerlinNoise
        from utils.geometry import generate_cylinder
        print("✓ Utility modules")
        
        # Test rendering (may fail if no OpenGL context)
        try:
            from rendering.shaders import SHADER_CONFIGS
            from rendering.camera import Camera
            from rendering.lighting import LightingSystem
            print("✓ Rendering modules")
        except Exception as e:
            print(f"⚠ Rendering modules (expected without OpenGL): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_lsystem():
    """Test L-System functionality"""
    print("\nTesting L-System...")
    
    try:
        # Create simple L-System
        lsystem = create_plant_lsystem('simple_tree')
        print(f"✓ Created L-System: {lsystem.axiom}")
        
        # Test iteration
        original = lsystem.current_string
        lsystem.iterate(1)
        print(f"✓ Iteration: '{original}' → '{lsystem.current_string}'")
        
        # Test interpretation
        from plant_system.lsystem import TurtleState
        initial_state = TurtleState(
            position=np.array([0, 0, 0]),
            direction=np.array([0, 1, 0]),
            up=np.array([0, 0, 1]),
            right=np.array([1, 0, 0]),
            radius=0.05,
            length=0.2
        )
        
        segments = lsystem.interpret(initial_state)
        print(f"✓ Interpreted {len(segments)} segments")
        
        return True
        
    except Exception as e:
        print(f"✗ L-System test failed: {e}")
        traceback.print_exc()
        return False

def test_plant_growth():
    """Test plant growth mechanics"""
    print("\nTesting plant growth...")
    
    try:
        # Create plant
        plant = Plant(plant_type='simple_tree')
        print(f"✓ Created plant with {len(plant.branches)} initial branches")
        
        # Test growth update
        initial_age = plant.age
        plant.update(0.1)  # 0.1 second update
        print(f"✓ Plant aged from {initial_age:.1f} to {plant.age:.1f}")
        
        # Test geometry generation
        branch_geom = plant.get_branch_geometry()
        if branch_geom[0] is not None:
            vertices, normals, texcoords, indices = branch_geom
            print(f"✓ Generated branch geometry: {len(vertices)} vertices, {len(indices)} indices")
        else:
            print("⚠ No branch geometry yet (expected for new plant)")
        
        # Test statistics
        stats = plant.get_stats()
        print(f"✓ Plant stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ Plant growth test failed: {e}")
        traceback.print_exc()
        return False

def test_math_utils():
    """Test mathematical utilities"""
    print("\nTesting math utilities...")
    
    try:
        from utils.math_utils import normalize, rotation_matrix_x, look_at_matrix
        
        # Test vector normalization
        vec = np.array([3, 4, 0])
        norm_vec = normalize(vec)
        length = np.linalg.norm(norm_vec)
        assert abs(length - 1.0) < 1e-6, f"Normalized vector length: {length}"
        print("✓ Vector normalization")
        
        # Test rotation matrix
        rot_mat = rotation_matrix_x(np.pi/2)
        test_vec = np.array([0, 1, 0])
        rotated = rot_mat @ test_vec
        expected = np.array([0, 0, 1])
        assert np.allclose(rotated, expected, atol=1e-6), f"Rotation failed: {rotated}"
        print("✓ Rotation matrix")
        
        # Test look-at matrix
        eye = np.array([0, 0, 5])
        target = np.array([0, 0, 0])
        up = np.array([0, 1, 0])
        view_mat = look_at_matrix(eye, target, up)
        assert view_mat.shape == (4, 4), f"View matrix shape: {view_mat.shape}"
        print("✓ Look-at matrix")
        
        return True
        
    except Exception as e:
        print(f"✗ Math utils test failed: {e}")
        traceback.print_exc()
        return False

def test_noise():
    """Test noise generation"""
    print("\nTesting noise generation...")
    
    try:
        from utils.noise import PerlinNoise, generate_bark_texture
        
        # Test Perlin noise
        noise_gen = PerlinNoise(octaves=4)
        noise_val = noise_gen.noise_2d(0.5, 0.5)
        assert -1 <= noise_val <= 1, f"Noise value out of range: {noise_val}"
        print("✓ Perlin noise generation")
        
        # Test texture generation
        bark_texture = generate_bark_texture(64, 64)
        assert bark_texture.shape == (64, 64, 3), f"Bark texture shape: {bark_texture.shape}"
        print("✓ Bark texture generation")
        
        return True
        
    except Exception as e:
        print(f"✗ Noise test failed: {e}")
        traceback.print_exc()
        return False

def test_geometry():
    """Test geometry generation"""
    print("\nTesting geometry generation...")
    
    try:
        from utils.geometry import generate_cylinder, generate_branch_geometry
        
        # Test cylinder generation
        vertices, normals, texcoords, indices = generate_cylinder(0.1, 0.05, 1.0, segments=8, rings=4)
        assert len(vertices) > 0, "No vertices generated"
        assert len(indices) > 0, "No indices generated"
        print(f"✓ Cylinder: {len(vertices)} vertices, {len(indices)} indices")
        
        # Test branch geometry
        start_pos = np.array([0, 0, 0])
        end_pos = np.array([0, 1, 0])
        vertices, normals, texcoords, indices = generate_branch_geometry(
            start_pos, end_pos, 0.1, 0.05, segments=8
        )
        assert len(vertices) > 0, "No branch vertices generated"
        print(f"✓ Branch: {len(vertices)} vertices, {len(indices)} indices")
        
        return True
        
    except Exception as e:
        print(f"✗ Geometry test failed: {e}")
        traceback.print_exc()
        return False

def test_camera():
    """Test camera system"""
    print("\nTesting camera system...")
    
    try:
        from rendering.camera import Camera
        
        # Create camera
        camera = Camera(target=[0, 1, 0], distance=3.0)
        print("✓ Camera created")
        
        # Test rotation
        camera.rotate(0.1, 0.1)
        print("✓ Camera rotation")
        
        # Test zoom
        camera.zoom(0.5)
        print("✓ Camera zoom")
        
        # Test matrices
        view_mat = camera.get_view_matrix()
        proj_mat = camera.get_projection_matrix()
        assert view_mat.shape == (4, 4), f"View matrix shape: {view_mat.shape}"
        assert proj_mat.shape == (4, 4), f"Projection matrix shape: {proj_mat.shape}"
        print("✓ Camera matrices")
        
        return True
        
    except Exception as e:
        print(f"✗ Camera test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=== Plant Growth Simulation Test Suite ===\n")
    
    tests = [
        test_imports,
        test_math_utils,
        test_noise,
        test_geometry,
        test_lsystem,
        test_plant_growth,
        test_camera
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The simulation should work correctly.")
        return 0
    else:
        print("⚠ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
