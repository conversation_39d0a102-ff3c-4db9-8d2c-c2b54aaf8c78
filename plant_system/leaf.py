"""
Leaf representation and behavior
"""
import numpy as np
from dataclasses import dataclass
from typing import Optional
from utils.math_utils import normalize, lerp, smoothstep
from utils.geometry import generate_leaf_geometry

@dataclass
class LeafProperties:
    """Properties of a leaf"""
    size: float = 0.1
    age: float = 0.0
    health: float = 1.0
    orientation: np.ndarray = None
    color: np.ndarray = None
    
    def __post_init__(self):
        if self.orientation is None:
            self.orientation = np.array([0, 1, 0])
        if self.color is None:
            self.color = np.array([0.2, 0.8, 0.2])  # Green

class Leaf:
    """Represents a single leaf with growth and aging"""
    
    def __init__(self, position, normal, initial_size=0.05, max_size=0.15):
        self.position = np.array(position)
        self.normal = normalize(np.array(normal))
        self.initial_size = initial_size
        self.max_size = max_size
        self.current_size = 0.0
        self.age = 0.0
        self.is_growing = True
        
        # Growth parameters
        self.growth_speed = 0.3  # Size units per second
        self.maturation_time = 5.0  # Time to reach full size
        self.lifespan = 30.0  # Total lifespan
        
        # Visual properties
        self.properties = LeafProperties(
            size=self.current_size,
            orientation=self.normal.copy()
        )
        
        # Environmental response
        self.light_direction = np.array([0, 1, 0])
        self.phototropism_strength = 0.5
        
    def update(self, dt, light_direction=None):
        """Update leaf growth and aging"""
        self.age += dt
        
        if light_direction is not None:
            self.light_direction = normalize(light_direction)
            self._update_orientation()
        
        # Growth phase
        if self.is_growing and self.current_size < self.max_size:
            growth_rate = self._calculate_growth_rate()
            self.current_size += growth_rate * dt
            
            if self.current_size >= self.max_size:
                self.current_size = self.max_size
                self.is_growing = False
        
        # Update properties
        self.properties.size = self.current_size
        self.properties.age = self.age
        self.properties.health = self._calculate_health()
        self.properties.color = self._calculate_color()
    
    def _calculate_growth_rate(self):
        """Calculate current growth rate based on age and conditions"""
        # Sigmoid growth curve
        t = self.age / self.maturation_time
        growth_factor = 1.0 / (1.0 + np.exp(-10 * (t - 0.5)))
        
        # Light influence on growth
        light_factor = max(0.3, np.dot(self.normal, self.light_direction))
        
        return self.growth_speed * growth_factor * light_factor
    
    def _update_orientation(self):
        """Update leaf orientation based on light direction"""
        # Gradually orient toward light
        target_normal = normalize(self.light_direction)
        
        # Interpolate between current normal and target
        blend_factor = self.phototropism_strength * 0.01  # Slow adjustment
        self.normal = normalize(lerp(self.normal, target_normal, blend_factor))
        self.properties.orientation = self.normal.copy()
    
    def _calculate_health(self):
        """Calculate leaf health based on age"""
        if self.age < self.maturation_time:
            return 1.0
        elif self.age < self.lifespan * 0.8:
            return 1.0
        else:
            # Gradual decline in health
            decline_start = self.lifespan * 0.8
            decline_factor = (self.age - decline_start) / (self.lifespan * 0.2)
            return max(0.0, 1.0 - decline_factor)
    
    def _calculate_color(self):
        """Calculate leaf color based on age and health"""
        # Young leaves are lighter green
        if self.age < self.maturation_time * 0.3:
            # Light green for young leaves
            base_color = np.array([0.4, 0.9, 0.4])
        elif self.age < self.maturation_time:
            # Transition to mature green
            t = (self.age - self.maturation_time * 0.3) / (self.maturation_time * 0.7)
            young_color = np.array([0.4, 0.9, 0.4])
            mature_color = np.array([0.2, 0.7, 0.2])
            base_color = lerp(young_color, mature_color, t)
        else:
            # Mature green
            base_color = np.array([0.2, 0.7, 0.2])
        
        # Health affects color intensity
        health_factor = self.properties.health
        
        # Aging effects
        if self.age > self.lifespan * 0.7:
            # Start turning yellow/brown
            age_factor = (self.age - self.lifespan * 0.7) / (self.lifespan * 0.3)
            autumn_color = np.array([0.8, 0.6, 0.2])  # Yellow-brown
            base_color = lerp(base_color, autumn_color, age_factor)
        
        return base_color * health_factor
    
    def get_geometry(self):
        """Generate 3D geometry for the leaf"""
        if self.current_size <= 0:
            return None, None, None, None
        
        return generate_leaf_geometry(
            self.position,
            self.normal,
            self.current_size
        )
    
    def is_alive(self):
        """Check if the leaf is still alive"""
        return self.age < self.lifespan and self.properties.health > 0.1
    
    def set_light_direction(self, light_dir):
        """Set the light direction for phototropism"""
        self.light_direction = normalize(light_dir)

class LeafCluster:
    """Manages multiple leaves at a branch tip"""
    
    def __init__(self, position, branch_direction, num_leaves=3):
        self.position = np.array(position)
        self.branch_direction = normalize(np.array(branch_direction))
        self.leaves = []
        
        # Create leaves around the branch tip
        for i in range(num_leaves):
            angle = 2 * np.pi * i / num_leaves
            
            # Create leaf normal (roughly perpendicular to branch)
            up = np.array([0, 1, 0])
            if abs(np.dot(self.branch_direction, up)) > 0.9:
                up = np.array([1, 0, 0])
            
            right = normalize(np.cross(self.branch_direction, up))
            up = normalize(np.cross(right, self.branch_direction))
            
            # Rotate around branch direction
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            leaf_normal = cos_a * right + sin_a * up
            
            # Add some randomness
            random_offset = (np.random.random(3) - 0.5) * 0.3
            leaf_normal = normalize(leaf_normal + random_offset)
            
            # Offset position slightly
            leaf_pos = self.position + leaf_normal * 0.02
            
            leaf = Leaf(leaf_pos, leaf_normal)
            self.leaves.append(leaf)
    
    def update(self, dt, light_direction=None):
        """Update all leaves in the cluster"""
        for leaf in self.leaves[:]:  # Copy list to allow removal
            leaf.update(dt, light_direction)
            
            # Remove dead leaves
            if not leaf.is_alive():
                self.leaves.remove(leaf)
    
    def get_geometry(self):
        """Get combined geometry for all leaves"""
        all_vertices = []
        all_normals = []
        all_texcoords = []
        all_indices = []
        vertex_offset = 0
        
        for leaf in self.leaves:
            geom = leaf.get_geometry()
            if geom[0] is not None:
                vertices, normals, texcoords, indices = geom
                
                indices += vertex_offset
                
                all_vertices.append(vertices)
                all_normals.append(normals)
                all_texcoords.append(texcoords)
                all_indices.append(indices)
                
                vertex_offset += len(vertices)
        
        if all_vertices:
            return (
                np.vstack(all_vertices),
                np.vstack(all_normals),
                np.vstack(all_texcoords),
                np.hstack(all_indices)
            )
        
        return None, None, None, None
    
    def is_empty(self):
        """Check if all leaves have died"""
        return len(self.leaves) == 0
