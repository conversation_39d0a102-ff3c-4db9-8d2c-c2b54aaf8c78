"""
L-System (Lindenmayer System) implementation for procedural plant growth
"""
import random
import numpy as np
from dataclasses import dataclass
from typing import Dict, List, Tuple
from utils.math_utils import rotation_matrix_axis_angle, normalize

@dataclass
class TurtleState:
    """State of the turtle graphics system"""
    position: np.ndarray
    direction: np.ndarray
    up: np.ndarray
    right: np.ndarray
    radius: float
    length: float
    age: float = 0.0
    
    def copy(self):
        return TurtleState(
            position=self.position.copy(),
            direction=self.direction.copy(),
            up=self.up.copy(),
            right=self.right.copy(),
            radius=self.radius,
            length=self.length,
            age=self.age
        )

class LSystem:
    """L-System for generating plant structures"""
    
    def __init__(self, axiom: str, rules: Dict[str, str], angle: float = 25.0):
        self.axiom = axiom
        self.rules = rules
        self.angle = np.radians(angle)  # Convert to radians
        self.current_string = axiom
        self.generation = 0
        
        # Growth parameters
        self.length_factor = 0.8  # How much shorter each generation gets
        self.radius_factor = 0.7  # How much thinner each generation gets
        self.randomness = 0.2     # Amount of random variation
    
    def iterate(self, iterations: int = 1):
        """Apply L-System rules for specified iterations"""
        for _ in range(iterations):
            new_string = ""
            for char in self.current_string:
                if char in self.rules:
                    # Add some randomness to rule application
                    if random.random() < 0.9:  # 90% chance to apply rule
                        new_string += self.rules[char]
                    else:
                        new_string += char  # Keep original character
                else:
                    new_string += char
            self.current_string = new_string
            self.generation += 1
    
    def interpret(self, initial_state: TurtleState) -> List[Tuple[str, TurtleState, TurtleState]]:
        """
        Interpret L-System string into turtle graphics commands
        Returns list of (command, start_state, end_state) tuples
        """
        state = initial_state.copy()
        stack = []
        segments = []
        
        for char in self.current_string:
            if char == 'F':  # Move forward and draw
                start_state = state.copy()
                
                # Add some natural variation
                length_variation = 1.0 + (random.random() - 0.5) * self.randomness
                radius_variation = 1.0 + (random.random() - 0.5) * self.randomness * 0.5
                
                # Calculate new position
                move_distance = state.length * length_variation
                new_position = state.position + state.direction * move_distance
                
                # Update state
                state.position = new_position
                state.radius *= self.radius_factor * radius_variation
                state.length *= self.length_factor
                state.age += 1.0
                
                end_state = state.copy()
                segments.append(('F', start_state, end_state))
                
            elif char == '+':  # Turn right around up axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.up, angle_variation)
                state.direction = normalize(rotation @ state.direction)
                state.right = normalize(rotation @ state.right)
                
            elif char == '-':  # Turn left around up axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.up, -angle_variation)
                state.direction = normalize(rotation @ state.direction)
                state.right = normalize(rotation @ state.right)
                
            elif char == '&':  # Pitch down around right axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.right, angle_variation)
                state.direction = normalize(rotation @ state.direction)
                state.up = normalize(rotation @ state.up)
                
            elif char == '^':  # Pitch up around right axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.right, -angle_variation)
                state.direction = normalize(rotation @ state.direction)
                state.up = normalize(rotation @ state.up)
                
            elif char == '\\':  # Roll left around direction axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.direction, angle_variation)
                state.up = normalize(rotation @ state.up)
                state.right = normalize(rotation @ state.right)
                
            elif char == '/':  # Roll right around direction axis
                angle_variation = self.angle + (random.random() - 0.5) * self.randomness
                rotation = rotation_matrix_axis_angle(state.direction, -angle_variation)
                state.up = normalize(rotation @ state.up)
                state.right = normalize(rotation @ state.right)
                
            elif char == '[':  # Push state onto stack
                stack.append(state.copy())
                
            elif char == ']':  # Pop state from stack
                if stack:
                    state = stack.pop()
                    
            elif char == 'L':  # Add leaf
                leaf_state = state.copy()
                segments.append(('L', leaf_state, leaf_state))
        
        return segments

# Predefined L-System rules for different plant types
PLANT_RULES = {
    'simple_tree': {
        'axiom': 'F',
        'rules': {
            'F': 'F[+F][-F]'
        },
        'angle': 25.0
    },
    
    'bushy_plant': {
        'axiom': 'F',
        'rules': {
            'F': 'FF[+F][-F][&F][^F]'
        },
        'angle': 30.0
    },
    
    'vine': {
        'axiom': 'F',
        'rules': {
            'F': 'F[+F]F[-F]F'
        },
        'angle': 22.5
    },
    
    'fern': {
        'axiom': 'X',
        'rules': {
            'X': 'F[+X][-X]FX',
            'F': 'FF'
        },
        'angle': 25.0
    },
    
    'realistic_tree': {
        'axiom': 'F',
        'rules': {
            'F': 'F[&+F][&-F][^+F][^-F]L'
        },
        'angle': 20.0
    }
}

def create_plant_lsystem(plant_type: str = 'realistic_tree') -> LSystem:
    """Create an L-System for a specific plant type"""
    if plant_type not in PLANT_RULES:
        plant_type = 'realistic_tree'
    
    config = PLANT_RULES[plant_type]
    return LSystem(
        axiom=config['axiom'],
        rules=config['rules'],
        angle=config['angle']
    )
