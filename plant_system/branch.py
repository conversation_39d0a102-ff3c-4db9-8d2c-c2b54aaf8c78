"""
Branch representation and growth mechanics
"""
import numpy as np
from dataclasses import dataclass
from typing import List, Optional
from utils.math_utils import normalize, lerp
from utils.geometry import generate_branch_geometry

@dataclass
class BranchNode:
    """A single node in the branch structure"""
    position: np.ndarray
    direction: np.ndarray
    radius: float
    age: float = 0.0
    growth_rate: float = 1.0
    parent: Optional['BranchNode'] = None
    children: List['BranchNode'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

class Branch:
    """Represents a single branch with growth mechanics"""
    
    def __init__(self, start_pos, start_direction, initial_radius=0.02, max_length=1.0):
        self.nodes = []
        self.start_pos = np.array(start_pos)
        self.direction = normalize(np.array(start_direction))
        self.initial_radius = initial_radius
        self.max_length = max_length
        self.current_length = 0.0
        self.growth_speed = 0.5  # Units per second
        self.is_growing = True
        self.age = 0.0
        
        # Growth parameters
        self.segment_length = 0.05  # Length of each segment
        self.radius_taper = 0.95    # How much radius decreases per segment
        self.phototropism_strength = 0.3  # Attraction to light
        self.gravitropism_strength = 0.1  # Upward growth bias
        self.apical_dominance = 0.8       # Main stem growth advantage
        
        # Environmental factors
        self.light_direction = np.array([0, 1, 0])  # Default light from above
        self.resources = 1.0  # Available growth resources
        
        # Create initial node
        initial_node = BranchNode(
            position=self.start_pos.copy(),
            direction=self.direction.copy(),
            radius=initial_radius,
            age=0.0
        )
        self.nodes.append(initial_node)
    
    def update(self, dt, light_direction=None, available_resources=1.0):
        """Update branch growth"""
        if not self.is_growing or self.current_length >= self.max_length:
            self.is_growing = False
            return
        
        self.age += dt
        self.resources = available_resources
        
        if light_direction is not None:
            self.light_direction = normalize(light_direction)
        
        # Calculate growth this frame
        growth_this_frame = self.growth_speed * dt * self.resources
        self.current_length += growth_this_frame
        
        # Check if we need to add a new segment
        if len(self.nodes) == 0:
            return
            
        last_node = self.nodes[-1]
        distance_from_start = np.linalg.norm(last_node.position - self.start_pos)
        
        if distance_from_start < self.current_length:
            self._add_new_segment(growth_this_frame)
    
    def _add_new_segment(self, growth_amount):
        """Add a new segment to the branch"""
        if not self.nodes:
            return
            
        last_node = self.nodes[-1]
        
        # Calculate new direction with environmental influences
        new_direction = self._calculate_growth_direction(last_node)
        
        # Calculate new position
        new_position = last_node.position + new_direction * self.segment_length
        
        # Calculate new radius (tapering)
        new_radius = last_node.radius * self.radius_taper
        
        # Create new node
        new_node = BranchNode(
            position=new_position,
            direction=new_direction,
            radius=new_radius,
            age=0.0,
            parent=last_node
        )
        
        last_node.children.append(new_node)
        self.nodes.append(new_node)
        
        # Secondary growth (thickening)
        self._apply_secondary_growth()
    
    def _calculate_growth_direction(self, current_node):
        """Calculate the direction for new growth"""
        base_direction = current_node.direction.copy()
        
        # Phototropism - grow toward light
        if np.linalg.norm(self.light_direction) > 0:
            light_influence = self.light_direction * self.phototropism_strength
            base_direction += light_influence
        
        # Gravitropism - slight upward bias
        gravity_influence = np.array([0, 1, 0]) * self.gravitropism_strength
        base_direction += gravity_influence
        
        # Add some random variation for natural look
        random_variation = (np.random.random(3) - 0.5) * 0.1
        base_direction += random_variation
        
        # Normalize the result
        return normalize(base_direction)
    
    def _apply_secondary_growth(self):
        """Apply secondary growth (thickening) to existing segments"""
        growth_factor = 1.001  # Small increase per frame
        
        for node in self.nodes[:-1]:  # Don't thicken the newest segment
            # Older segments grow thicker
            age_factor = min(node.age * 0.1, 1.0)
            node.radius *= (1.0 + age_factor * 0.001)
            node.age += 0.1
    
    def get_geometry(self):
        """Generate 3D geometry for the entire branch"""
        if len(self.nodes) < 2:
            return None, None, None, None
        
        all_vertices = []
        all_normals = []
        all_texcoords = []
        all_indices = []
        vertex_offset = 0
        
        for i in range(len(self.nodes) - 1):
            start_node = self.nodes[i]
            end_node = self.nodes[i + 1]
            
            vertices, normals, texcoords, indices = generate_branch_geometry(
                start_node.position,
                end_node.position,
                start_node.radius,
                end_node.radius,
                segments=8
            )
            
            # Offset indices for this segment
            indices += vertex_offset
            
            all_vertices.append(vertices)
            all_normals.append(normals)
            all_texcoords.append(texcoords)
            all_indices.append(indices)
            
            vertex_offset += len(vertices)
        
        if all_vertices:
            return (
                np.vstack(all_vertices),
                np.vstack(all_normals),
                np.vstack(all_texcoords),
                np.hstack(all_indices)
            )
        
        return None, None, None, None
    
    def get_tip_position(self):
        """Get the position of the growing tip"""
        if self.nodes:
            return self.nodes[-1].position.copy()
        return self.start_pos.copy()
    
    def get_tip_direction(self):
        """Get the direction of the growing tip"""
        if self.nodes:
            return self.nodes[-1].direction.copy()
        return self.direction.copy()
    
    def set_light_direction(self, light_dir):
        """Set the light direction for phototropism"""
        self.light_direction = normalize(light_dir)
    
    def set_resources(self, resources):
        """Set available resources for growth"""
        self.resources = max(0.0, min(1.0, resources))
        
    def stop_growth(self):
        """Stop the growth of this branch"""
        self.is_growing = False
