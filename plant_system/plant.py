"""
Main plant class that orchestrates growth using L-Systems and realistic mechanics
"""
import numpy as np
from typing import List, Dict, Optional
import time

from .lsystem import LSystem, TurtleState, create_plant_lsystem
from .branch import Branch
from .leaf import Leaf, LeafCluster
from utils.math_utils import normalize

class Plant:
    """Main plant class with realistic growth simulation"""
    
    def __init__(self, plant_type='realistic_tree', position=None):
        self.plant_type = plant_type
        self.position = np.array(position) if position else np.array([0, 0, 0])
        
        # L-System for growth pattern
        self.lsystem = create_plant_lsystem(plant_type)
        
        # Plant components
        self.branches = []
        self.leaf_clusters = []
        
        # Growth parameters
        self.age = 0.0
        self.growth_speed = 1.0
        self.max_iterations = 6
        self.current_iteration = 0
        self.time_between_iterations = 5.0  # Seconds between L-System iterations
        self.last_iteration_time = 0.0
        
        # Environmental factors
        self.light_direction = np.array([0.3, 1.0, 0.2])  # Slightly angled sunlight
        self.light_direction = normalize(self.light_direction)
        self.gravity = np.array([0, -1, 0])
        
        # Resource system
        self.total_resources = 1.0
        self.resource_distribution = {}
        
        # Initialize with trunk
        self._initialize_trunk()
        
    def _initialize_trunk(self):
        """Initialize the main trunk"""
        initial_state = TurtleState(
            position=self.position.copy(),
            direction=np.array([0, 1, 0]),  # Grow upward
            up=np.array([0, 0, 1]),
            right=np.array([1, 0, 0]),
            radius=0.05,  # Initial trunk radius
            length=0.2    # Initial segment length
        )
        
        # Create main trunk branch
        trunk = Branch(
            start_pos=self.position,
            start_direction=initial_state.direction,
            initial_radius=initial_state.radius,
            max_length=2.0
        )
        trunk.growth_speed = 0.3
        trunk.set_light_direction(self.light_direction)
        
        self.branches.append(trunk)
        self.main_trunk = trunk
        
    def update(self, dt):
        """Update plant growth and aging"""
        self.age += dt
        
        # Check if it's time for next L-System iteration
        if (self.age - self.last_iteration_time >= self.time_between_iterations and 
            self.current_iteration < self.max_iterations):
            self._grow_iteration()
            self.last_iteration_time = self.age
            self.current_iteration += 1
        
        # Update resource distribution
        self._update_resources()
        
        # Update all branches
        for branch in self.branches:
            resources = self.resource_distribution.get(id(branch), 0.5)
            branch.update(dt, self.light_direction, resources)
        
        # Update leaf clusters
        for cluster in self.leaf_clusters[:]:
            cluster.update(dt, self.light_direction)
            if cluster.is_empty():
                self.leaf_clusters.remove(cluster)
        
        # Occasionally add new leaves to growing tips
        if np.random.random() < 0.1 * dt:  # 10% chance per second
            self._maybe_add_leaves()
    
    def _grow_iteration(self):
        """Perform one iteration of L-System growth"""
        print(f"Growing iteration {self.current_iteration + 1}")
        
        # Apply L-System rules
        self.lsystem.iterate(1)
        
        # Get current state from main trunk tip
        trunk_tip = self.main_trunk.get_tip_position()
        trunk_direction = self.main_trunk.get_tip_direction()
        
        initial_state = TurtleState(
            position=trunk_tip,
            direction=trunk_direction,
            up=np.array([0, 0, 1]),
            right=normalize(np.cross(trunk_direction, np.array([0, 0, 1]))),
            radius=self.main_trunk.nodes[-1].radius if self.main_trunk.nodes else 0.03,
            length=0.15
        )
        
        # Interpret L-System and create new branches
        segments = self.lsystem.interpret(initial_state)
        
        for command, start_state, end_state in segments:
            if command == 'F':  # Create branch segment
                new_branch = Branch(
                    start_pos=start_state.position,
                    start_direction=start_state.direction,
                    initial_radius=start_state.radius,
                    max_length=start_state.length * 3
                )
                new_branch.set_light_direction(self.light_direction)
                new_branch.growth_speed = 0.2 * (1.0 - self.current_iteration * 0.1)
                self.branches.append(new_branch)
                
            elif command == 'L':  # Create leaf cluster
                cluster = LeafCluster(
                    position=start_state.position,
                    branch_direction=start_state.direction,
                    num_leaves=np.random.randint(2, 5)
                )
                self.leaf_clusters.append(cluster)
    
    def _update_resources(self):
        """Update resource distribution among branches"""
        if not self.branches:
            return
        
        # Simple resource model: newer branches get less resources
        total_branches = len(self.branches)
        
        for i, branch in enumerate(self.branches):
            if i == 0:  # Main trunk gets most resources
                resources = 0.8
            else:
                # Newer branches get progressively fewer resources
                age_factor = max(0.1, 1.0 - (i / total_branches))
                resources = 0.3 * age_factor
            
            self.resource_distribution[id(branch)] = resources
    
    def _maybe_add_leaves(self):
        """Randomly add leaves to growing branch tips"""
        growing_branches = [b for b in self.branches if b.is_growing and len(b.nodes) > 3]
        
        if growing_branches:
            branch = np.random.choice(growing_branches)
            tip_pos = branch.get_tip_position()
            tip_dir = branch.get_tip_direction()
            
            # Don't add leaves too close to existing clusters
            too_close = False
            for cluster in self.leaf_clusters:
                if np.linalg.norm(cluster.position - tip_pos) < 0.3:
                    too_close = True
                    break
            
            if not too_close:
                cluster = LeafCluster(
                    position=tip_pos,
                    branch_direction=tip_dir,
                    num_leaves=np.random.randint(1, 4)
                )
                self.leaf_clusters.append(cluster)

    def get_branch_geometry(self):
        """Get combined geometry for all branches"""
        all_vertices = []
        all_normals = []
        all_texcoords = []
        all_indices = []
        vertex_offset = 0

        for branch in self.branches:
            geom = branch.get_geometry()
            if geom[0] is not None:
                vertices, normals, texcoords, indices = geom

                indices += vertex_offset

                all_vertices.append(vertices)
                all_normals.append(normals)
                all_texcoords.append(texcoords)
                all_indices.append(indices)

                vertex_offset += len(vertices)

        if all_vertices:
            return (
                np.vstack(all_vertices),
                np.vstack(all_normals),
                np.vstack(all_texcoords),
                np.hstack(all_indices)
            )

        return None, None, None, None

    def get_leaf_geometry(self):
        """Get combined geometry for all leaves"""
        all_vertices = []
        all_normals = []
        all_texcoords = []
        all_indices = []
        vertex_offset = 0

        for cluster in self.leaf_clusters:
            geom = cluster.get_geometry()
            if geom[0] is not None:
                vertices, normals, texcoords, indices = geom

                indices += vertex_offset

                all_vertices.append(vertices)
                all_normals.append(normals)
                all_texcoords.append(texcoords)
                all_indices.append(indices)

                vertex_offset += len(vertices)

        if all_vertices:
            return (
                np.vstack(all_vertices),
                np.vstack(all_normals),
                np.vstack(all_texcoords),
                np.hstack(all_indices)
            )

        return None, None, None, None

    def set_light_direction(self, direction):
        """Set the light direction for the entire plant"""
        self.light_direction = normalize(direction)

        for branch in self.branches:
            branch.set_light_direction(self.light_direction)

        for cluster in self.leaf_clusters:
            for leaf in cluster.leaves:
                leaf.set_light_direction(self.light_direction)

    def set_growth_speed(self, speed):
        """Set the overall growth speed multiplier"""
        self.growth_speed = max(0.0, speed)

        for branch in self.branches:
            branch.growth_speed = 0.3 * self.growth_speed

    def reset(self):
        """Reset the plant to initial state"""
        self.age = 0.0
        self.current_iteration = 0
        self.last_iteration_time = 0.0
        self.branches.clear()
        self.leaf_clusters.clear()
        self.resource_distribution.clear()

        # Reset L-System
        self.lsystem = create_plant_lsystem(self.plant_type)

        # Reinitialize
        self._initialize_trunk()

    def get_stats(self):
        """Get plant statistics"""
        return {
            'age': self.age,
            'iteration': self.current_iteration,
            'branches': len(self.branches),
            'leaf_clusters': len(self.leaf_clusters),
            'total_leaves': sum(len(cluster.leaves) for cluster in self.leaf_clusters),
            'growing_branches': sum(1 for b in self.branches if b.is_growing)
        }
