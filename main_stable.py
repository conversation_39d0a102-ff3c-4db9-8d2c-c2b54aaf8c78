"""
Ultra-Stable 3D Plant Growth Simulation
Conservative approach to ensure compatibility
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys
import math

from plant_system.plant import Plant

class StablePlantSimulation:
    """Ultra-stable plant simulation with minimal rendering"""
    
    def __init__(self, width=800, height=600):
        self.width = width
        self.height = height
        self.running = True
        
        print("🌱 Initializing Ultra-Stable Plant Simulation...")
        
        # Initialize Pygame with minimal settings
        pygame.init()
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        
        self.screen = pygame.display.set_mode((width, height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption("🌳 Stable Plant Growth Simulation")
        
        # Create OpenGL context
        self.ctx = mgl.create_context()
        self.ctx.enable(mgl.DEPTH_TEST)
        
        print(f"✓ OpenGL: {self.ctx.info['GL_VERSION']}")
        print(f"✓ GPU: {self.ctx.info['GL_RENDERER']}")
        
        # Create minimal shader
        vertex_shader = """
        #version 330 core
        layout (location = 0) in vec3 aPos;
        uniform mat4 mvp;
        void main() {
            gl_Position = mvp * vec4(aPos, 1.0);
        }
        """
        
        fragment_shader = """
        #version 330 core
        out vec4 FragColor;
        uniform vec3 color;
        void main() {
            FragColor = vec4(color, 1.0);
        }
        """
        
        try:
            self.program = self.ctx.program(vertex_shader=vertex_shader, fragment_shader=fragment_shader)
            print("✓ Shader program created")
        except Exception as e:
            print(f"❌ Shader creation failed: {e}")
            raise
        
        # Create plant
        try:
            self.plant = Plant(plant_type='simple_tree', position=[0, 0, 0])
            print("✓ Plant created")
        except Exception as e:
            print(f"❌ Plant creation failed: {e}")
            raise
        
        # Camera parameters
        self.camera_distance = 3.0
        self.camera_angle_x = 0.0
        self.camera_angle_y = 0.4
        
        # Control state
        self.keys = {}
        self.mouse_buttons = [False, False, False]
        self.paused = False
        self.growth_speed = 1.0
        self.show_stats = True
        self.clock = pygame.time.Clock()
        
        # Rendering buffers (reused to prevent memory issues)
        self.current_vao = None
        self.current_vbo = None
        
        print("✓ Stable simulation ready!")
    
    def get_mvp_matrix(self):
        """Calculate model-view-projection matrix"""
        # Camera position
        cam_x = self.camera_distance * math.sin(self.camera_angle_y) * math.cos(self.camera_angle_x)
        cam_y = self.camera_distance * math.cos(self.camera_angle_y)
        cam_z = self.camera_distance * math.sin(self.camera_angle_y) * math.sin(self.camera_angle_x)
        
        # Simple view matrix
        eye = np.array([cam_x, cam_y, cam_z])
        target = np.array([0, 1, 0])
        up = np.array([0, 1, 0])
        
        forward = target - eye
        forward = forward / np.linalg.norm(forward)
        right = np.cross(forward, up)
        right = right / np.linalg.norm(right)
        up = np.cross(right, forward)
        
        view = np.eye(4)
        view[0, :3] = right
        view[1, :3] = up
        view[2, :3] = -forward
        view[:3, 3] = -np.array([np.dot(right, eye), np.dot(up, eye), np.dot(forward, eye)])
        
        # Projection matrix
        fov = math.radians(45)
        aspect = self.width / self.height
        near, far = 0.1, 100.0
        
        f = 1.0 / math.tan(fov / 2.0)
        proj = np.zeros((4, 4))
        proj[0, 0] = f / aspect
        proj[1, 1] = f
        proj[2, 2] = (far + near) / (near - far)
        proj[2, 3] = (2 * far * near) / (near - far)
        proj[3, 2] = -1
        
        return (proj @ view).astype(np.float32)
    
    def safe_render_lines(self, vertices, color):
        """Safely render lines with error handling"""
        if len(vertices) == 0:
            return
        
        try:
            # Clean up previous buffers
            if self.current_vao:
                self.current_vao.release()
            if self.current_vbo:
                self.current_vbo.release()
            
            # Create new buffers
            vertex_data = vertices.astype(np.float32)
            self.current_vbo = self.ctx.buffer(vertex_data.tobytes())
            self.current_vao = self.ctx.vertex_array(self.program, [(self.current_vbo, '3f', 'aPos')])
            
            # Set uniforms
            mvp = self.get_mvp_matrix()
            self.program['mvp'].write(mvp.tobytes())
            self.program['color'].value = color
            
            # Render as lines
            self.current_vao.render(mode=mgl.LINES)
            
        except Exception as e:
            print(f"Render error: {e}")
    
    def render_plant_simple(self):
        """Render plant as simple wireframe"""
        try:
            # Get branch geometry
            branch_geom = self.plant.get_branch_geometry()
            if branch_geom[0] is not None:
                vertices, normals, texcoords, indices = branch_geom
                
                # Convert triangles to lines for wireframe
                lines = []
                for i in range(0, len(indices) - 2, 3):
                    if i + 2 < len(indices):
                        a, b, c = indices[i], indices[i+1], indices[i+2]
                        if a < len(vertices) and b < len(vertices) and c < len(vertices):
                            lines.extend([vertices[a], vertices[b]])
                            lines.extend([vertices[b], vertices[c]])
                            lines.extend([vertices[c], vertices[a]])
                
                if lines:
                    line_vertices = np.array(lines)
                    self.safe_render_lines(line_vertices, (0.6, 0.4, 0.2))  # Brown
            
            # Render leaves as simple points/lines
            leaf_geom = self.plant.get_leaf_geometry()
            if leaf_geom[0] is not None:
                vertices, normals, texcoords, indices = leaf_geom
                if len(vertices) > 0:
                    # Just render some leaf vertices as lines
                    leaf_lines = []
                    for i in range(0, len(vertices) - 1, 2):
                        if i + 1 < len(vertices):
                            leaf_lines.extend([vertices[i], vertices[i+1]])
                    
                    if leaf_lines:
                        leaf_line_vertices = np.array(leaf_lines)
                        self.safe_render_lines(leaf_line_vertices, (0.2, 0.8, 0.2))  # Green
        
        except Exception as e:
            print(f"Plant render error: {e}")
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    print(f"{'⏸️ Paused' if self.paused else '▶️ Resumed'}")
                elif event.key == pygame.K_r:
                    self.plant.reset()
                    print("🔄 Plant reset")
                elif event.key == pygame.K_h:
                    self.show_stats = not self.show_stats
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.growth_speed = min(5.0, self.growth_speed * 1.2)
                    print(f"⚡ Growth speed: {self.growth_speed:.1f}x")
                elif event.key == pygame.K_MINUS:
                    self.growth_speed = max(0.1, self.growth_speed / 1.2)
                    print(f"🐌 Growth speed: {self.growth_speed:.1f}x")
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.mouse_buttons[event.button - 1] = True
            
            elif event.type == pygame.MOUSEBUTTONUP:
                self.mouse_buttons[event.button - 1] = False
            
            elif event.type == pygame.MOUSEWHEEL:
                self.camera_distance = max(1.0, min(10.0, self.camera_distance - event.y * 0.3))
            
            elif event.type == pygame.MOUSEMOTION:
                if self.mouse_buttons[0]:  # Left mouse button
                    self.camera_angle_x += event.rel[0] * 0.01
                    self.camera_angle_y = max(0.1, min(math.pi - 0.1, 
                                                     self.camera_angle_y + event.rel[1] * 0.01))
    
    def run(self):
        """Main application loop"""
        print("\n🚀 Starting Ultra-Stable Plant Simulation!")
        print("\n🎮 Controls:")
        print("  🖱️  Mouse: Rotate camera")
        print("  🎡 Mouse wheel: Zoom")
        print("  ⏸️  Space: Pause/Resume")
        print("  🔄 R: Reset plant")
        print("  ⚡ +/-: Growth speed")
        print("  📊 H: Toggle stats")
        print("  🚪 ESC: Exit")
        print()
        
        last_stats_time = time.time()
        frame_count = 0
        
        try:
            while self.running:
                frame_count += 1
                dt = self.clock.tick(30) / 1000.0  # Lower FPS for stability
                
                # Handle events
                self.handle_events()
                
                # Update plant
                if not self.paused:
                    self.plant.update(dt * self.growth_speed)
                
                # Render
                try:
                    self.ctx.clear(0.5, 0.7, 1.0, 1.0)  # Sky blue
                    self.render_plant_simple()
                    pygame.display.flip()
                except Exception as e:
                    print(f"Frame {frame_count} render error: {e}")
                    # Continue anyway
                
                # Print stats
                if self.show_stats:
                    current_time = time.time()
                    if current_time - last_stats_time >= 5.0:
                        stats = self.plant.get_stats()
                        fps = self.clock.get_fps()
                        print(f"🌱 Age: {stats['age']:.1f}s | 🌿 Branches: {stats['branches']} | "
                              f"🔄 Iteration: {stats['iteration']} | 🍃 Leaves: {stats['total_leaves']} | "
                              f"📈 FPS: {fps:.1f} | Frame: {frame_count}")
                        last_stats_time = current_time
        
        except KeyboardInterrupt:
            print("\n👋 Simulation stopped by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            # Cleanup
            try:
                if self.current_vao:
                    self.current_vao.release()
                if self.current_vbo:
                    self.current_vbo.release()
                pygame.quit()
            except:
                pass
        
        print("✅ Simulation ended successfully")
        return 0

def main():
    """Main entry point"""
    try:
        simulation = StablePlantSimulation(800, 600)
        return simulation.run()
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
