"""
Working 3D Plant Growth Simulation with simplified textures
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys

from plant_system.plant import Plant
from rendering.renderer import PlantRenderer

class WorkingPlantSimulation:
    """Plant simulation with guaranteed working textures"""
    
    def __init__(self, width=1000, height=700):
        self.width = width
        self.height = height
        self.running = True
        
        print("Initializing Working Plant Simulation...")
        
        # Initialize Pygame
        pygame.init()
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_PROFILE_MASK, pygame.GL_CONTEXT_PROFILE_CORE)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        pygame.display.gl_set_attribute(pygame.GL_DEPTH_SIZE, 24)
        
        self.screen = pygame.display.set_mode((width, height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption("3D Plant Growth Simulation - Working Version")
        
        print(f"OpenGL Version: {pygame.display.gl_get_attribute(pygame.GL_CONTEXT_MAJOR_VERSION)}.{pygame.display.gl_get_attribute(pygame.GL_CONTEXT_MINOR_VERSION)}")
        
        # Create ModernGL context
        self.ctx = mgl.create_context()
        print(f"ModernGL context: {self.ctx.info['GL_VERSION']}")
        print(f"GPU: {self.ctx.info['GL_RENDERER']}")
        
        # Initialize renderer with simple textures
        self.renderer = PlantRenderer(width, height)
        self.renderer.initialize(self.ctx)
        
        # Create plant
        self.plant = Plant(plant_type='realistic_tree', position=[0, 0, 0])
        
        # Control state
        self.keys = {}
        self.mouse_buttons = [False, False, False]
        self.paused = False
        self.growth_speed = 1.0
        self.show_stats = True
        self.clock = pygame.time.Clock()
        self.fps_target = 60
        
        print("✓ Working simulation initialized!")
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                self.keys[event.key] = True
                
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    print(f"Simulation {'paused' if self.paused else 'resumed'}")
                elif event.key == pygame.K_r:
                    self.plant.reset()
                    print("Plant reset")
                elif event.key == pygame.K_f:
                    self.renderer.wireframe_mode = not self.renderer.wireframe_mode
                    print(f"Wireframe mode: {self.renderer.wireframe_mode}")
                elif event.key == pygame.K_h:
                    self.show_stats = not self.show_stats
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.growth_speed = min(5.0, self.growth_speed * 1.2)
                    self.plant.set_growth_speed(self.growth_speed)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
                elif event.key == pygame.K_MINUS:
                    self.growth_speed = max(0.1, self.growth_speed / 1.2)
                    self.plant.set_growth_speed(self.growth_speed)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
            
            elif event.type == pygame.KEYUP:
                self.keys[event.key] = False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.mouse_buttons[event.button - 1] = True
            
            elif event.type == pygame.MOUSEBUTTONUP:
                self.mouse_buttons[event.button - 1] = False
            
            elif event.type == pygame.MOUSEWHEEL:
                self.renderer.camera.zoom(event.y * -0.5)
            
            elif event.type == pygame.MOUSEMOTION:
                if self.mouse_buttons[0]:  # Left mouse button
                    dx = event.rel[0]
                    dy = event.rel[1]
                    self.renderer.camera.rotate(dx, dy)
                elif self.mouse_buttons[2]:  # Right mouse button
                    dx = event.rel[0]
                    dy = event.rel[1]
                    self.renderer.camera.pan(-dx, dy)
            
            elif event.type == pygame.VIDEORESIZE:
                self.width = event.w
                self.height = event.h
                self.renderer.resize(self.width, self.height)
    
    def handle_continuous_input(self):
        """Handle continuous input (held keys)"""
        pan_speed = 50.0 * (self.clock.get_time() / 1000.0)
        if self.keys.get(pygame.K_w, False):
            self.renderer.camera.pan(0, pan_speed)
        if self.keys.get(pygame.K_s, False):
            self.renderer.camera.pan(0, -pan_speed)
        if self.keys.get(pygame.K_a, False):
            self.renderer.camera.pan(-pan_speed, 0)
        if self.keys.get(pygame.K_d, False):
            self.renderer.camera.pan(pan_speed, 0)
        
        # Light direction control
        if self.keys.get(pygame.K_LEFT, False):
            current_light = self.renderer.lighting.get_primary_light()
            if current_light and hasattr(current_light, 'direction'):
                angle = 0.5 * (self.clock.get_time() / 1000.0)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                x, y, z = current_light.direction
                new_x = x * cos_a - z * sin_a
                new_z = x * sin_a + z * cos_a
                current_light.set_direction([new_x, y, new_z])
                self.plant.set_light_direction([new_x, y, new_z])
        
        if self.keys.get(pygame.K_RIGHT, False):
            current_light = self.renderer.lighting.get_primary_light()
            if current_light and hasattr(current_light, 'direction'):
                angle = -0.5 * (self.clock.get_time() / 1000.0)
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                x, y, z = current_light.direction
                new_x = x * cos_a - z * sin_a
                new_z = x * sin_a + z * cos_a
                current_light.set_direction([new_x, y, new_z])
                self.plant.set_light_direction([new_x, y, new_z])
    
    def update(self, dt):
        """Update simulation"""
        if not self.paused:
            self.plant.update(dt * self.growth_speed)
    
    def render(self):
        """Render the scene"""
        dt = self.clock.get_time() / 1000.0
        self.renderer.render(self.plant, dt)
        pygame.display.flip()
    
    def run(self):
        """Main application loop"""
        print("Starting 3D plant growth simulation...")
        print("\nControls:")
        print("  Mouse: Rotate camera")
        print("  Mouse wheel: Zoom")
        print("  WASD: Pan camera")
        print("  Arrow keys: Move light")
        print("  Space: Pause/Resume")
        print("  R: Reset plant")
        print("  +/-: Growth speed")
        print("  F: Wireframe")
        print("  H: Toggle stats")
        print("  ESC: Exit")
        print()
        
        last_stats_time = time.time()
        
        try:
            while self.running:
                dt = self.clock.tick(self.fps_target) / 1000.0
                
                # Handle events
                self.handle_events()
                self.handle_continuous_input()
                
                # Update simulation
                self.update(dt)
                
                # Render
                self.render()
                
                # Print stats every 5 seconds
                if self.show_stats:
                    current_time = time.time()
                    if current_time - last_stats_time >= 5.0:
                        stats = self.plant.get_stats()
                        fps = self.clock.get_fps()
                        print(f"Age: {stats['age']:.1f}s, Branches: {stats['branches']}, "
                              f"Iteration: {stats['iteration']}, Leaves: {stats['total_leaves']}, FPS: {fps:.1f}")
                        last_stats_time = current_time
        
        except KeyboardInterrupt:
            print("\nSimulation interrupted by user")
        except Exception as e:
            print(f"\nSimulation error: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            self.cleanup()
        
        print("Simulation ended")
        return 0
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'renderer'):
                self.renderer.cleanup()
            pygame.quit()
        except Exception as e:
            print(f"Cleanup error: {e}")

def main():
    """Main entry point"""
    try:
        simulation = WorkingPlantSimulation(1000, 700)
        return simulation.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
