"""
OpenGL shaders for realistic plant rendering
"""

# Vertex shader for branches (bark)
BRANCH_VERTEX_SHADER = """
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoord;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
uniform mat3 normalMatrix;

out vec3 FragPos;
out vec3 Normal;
out vec2 TexCoord;

void main()
{
    FragPos = vec3(model * vec4(aPos, 1.0));
    Normal = normalMatrix * aNormal;
    TexCoord = aTexCoord;
    
    gl_Position = projection * view * vec4(FragPos, 1.0);
}
"""

# Fragment shader for branches with Phong lighting
BRANCH_FRAGMENT_SHADER = """
#version 330 core

in vec3 FragPos;
in vec3 Normal;
in vec2 TexCoord;

out vec4 FragColor;

uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 viewPos;
uniform sampler2D barkTexture;

// Material properties
uniform float ambientStrength;
uniform float specularStrength;
uniform int shininess;

void main()
{
    // Sample bark texture
    vec3 textureColor = texture(barkTexture, TexCoord).rgb;
    
    // Ambient lighting
    vec3 ambient = ambientStrength * lightColor * textureColor;
    
    // Diffuse lighting
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(lightPos - FragPos);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * lightColor * textureColor;
    
    // Specular lighting (reduced for bark)
    vec3 viewDir = normalize(viewPos - FragPos);
    vec3 reflectDir = reflect(-lightDir, norm);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), shininess);
    vec3 specular = specularStrength * spec * lightColor;
    
    // Combine results
    vec3 result = ambient + diffuse + specular;
    
    // Add some subsurface scattering effect for organic look
    float subsurface = max(0.0, dot(-lightDir, norm)) * 0.3;
    result += subsurface * lightColor * textureColor;
    
    FragColor = vec4(result, 1.0);
}
"""

# Vertex shader for leaves
LEAF_VERTEX_SHADER = """
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoord;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
uniform mat3 normalMatrix;
uniform float time;

out vec3 FragPos;
out vec3 Normal;
out vec2 TexCoord;

void main()
{
    vec3 pos = aPos;
    
    // Add subtle wind animation
    float windStrength = 0.02;
    float windSpeed = 2.0;
    pos.x += sin(time * windSpeed + pos.y * 10.0) * windStrength;
    pos.z += cos(time * windSpeed * 0.7 + pos.x * 8.0) * windStrength * 0.5;
    
    FragPos = vec3(model * vec4(pos, 1.0));
    Normal = normalMatrix * aNormal;
    TexCoord = aTexCoord;
    
    gl_Position = projection * view * vec4(FragPos, 1.0);
}
"""

# Fragment shader for leaves with subsurface scattering
LEAF_FRAGMENT_SHADER = """
#version 330 core

in vec3 FragPos;
in vec3 Normal;
in vec2 TexCoord;

out vec4 FragColor;

uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 viewPos;
uniform sampler2D leafTexture;
uniform vec3 leafColor;

// Material properties
uniform float ambientStrength;
uniform float subsurfaceStrength;

void main()
{
    // Sample leaf texture for alpha and detail
    vec4 textureColor = texture(leafTexture, TexCoord);
    
    // Use texture alpha for leaf shape
    if (textureColor.a < 0.1)
        discard;
    
    // Base leaf color
    vec3 baseColor = leafColor * textureColor.rgb;
    
    // Ambient lighting
    vec3 ambient = ambientStrength * lightColor * baseColor;
    
    // Diffuse lighting
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(lightPos - FragPos);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * lightColor * baseColor;
    
    // Subsurface scattering for leaves
    float subsurface = max(0.0, dot(-lightDir, norm));
    vec3 subsurfaceColor = subsurface * subsurfaceStrength * lightColor * baseColor;
    
    // Edge lighting effect
    vec3 viewDir = normalize(viewPos - FragPos);
    float fresnel = 1.0 - max(0.0, dot(viewDir, norm));
    vec3 edgeLight = fresnel * 0.3 * lightColor * baseColor;
    
    // Combine results
    vec3 result = ambient + diffuse + subsurfaceColor + edgeLight;
    
    // Apply some color variation based on texture
    result *= (0.8 + 0.4 * textureColor.rgb);
    
    FragColor = vec4(result, textureColor.a);
}
"""

# Simple vertex shader for debugging
DEBUG_VERTEX_SHADER = """
#version 330 core

layout (location = 0) in vec3 aPos;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

void main()
{
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"""

# Simple fragment shader for debugging
DEBUG_FRAGMENT_SHADER = """
#version 330 core

out vec4 FragColor;

uniform vec3 color;

void main()
{
    FragColor = vec4(color, 1.0);
}
"""

# Skybox vertex shader
SKYBOX_VERTEX_SHADER = """
#version 330 core

layout (location = 0) in vec3 aPos;

out vec3 TexCoords;

uniform mat4 view;
uniform mat4 projection;

void main()
{
    TexCoords = aPos;
    vec4 pos = projection * view * vec4(aPos, 1.0);
    gl_Position = pos.xyww;  // Ensure skybox is always at far plane
}
"""

# Skybox fragment shader
SKYBOX_FRAGMENT_SHADER = """
#version 330 core

out vec4 FragColor;

in vec3 TexCoords;

uniform vec3 skyColor;
uniform vec3 horizonColor;
uniform vec3 groundColor;

void main()
{
    float y = normalize(TexCoords).y;
    
    vec3 color;
    if (y > 0.0) {
        // Sky gradient
        color = mix(horizonColor, skyColor, y);
    } else {
        // Ground gradient
        color = mix(horizonColor, groundColor, -y);
    }
    
    FragColor = vec4(color, 1.0);
}
"""

def compile_shader(source, shader_type):
    """Compile a shader from source code"""
    import moderngl as mgl
    
    # This is a placeholder - actual compilation happens in the renderer
    return source

def create_shader_program(vertex_source, fragment_source):
    """Create a shader program from vertex and fragment sources"""
    import moderngl as mgl
    
    # This is a placeholder - actual program creation happens in the renderer
    return {
        'vertex': vertex_source,
        'fragment': fragment_source
    }

# Shader configurations
SHADER_CONFIGS = {
    'branch': {
        'vertex': BRANCH_VERTEX_SHADER,
        'fragment': BRANCH_FRAGMENT_SHADER,
        'uniforms': {
            'ambientStrength': 0.3,
            'specularStrength': 0.1,
            'shininess': 8
        }
    },
    'leaf': {
        'vertex': LEAF_VERTEX_SHADER,
        'fragment': LEAF_FRAGMENT_SHADER,
        'uniforms': {
            'ambientStrength': 0.4,
            'subsurfaceStrength': 0.6
        }
    },
    'debug': {
        'vertex': DEBUG_VERTEX_SHADER,
        'fragment': DEBUG_FRAGMENT_SHADER,
        'uniforms': {}
    },
    'skybox': {
        'vertex': SKYBOX_VERTEX_SHADER,
        'fragment': SKYBOX_FRAGMENT_SHADER,
        'uniforms': {
            'skyColor': [0.5, 0.7, 1.0],
            'horizonColor': [0.8, 0.9, 1.0],
            'groundColor': [0.3, 0.5, 0.3]
        }
    }
}
