"""
Main OpenGL renderer for 3D plant visualization
"""
import numpy as np
import moderngl as mgl
from PIL import Image
import time

from .shaders import SHADER_CONFIGS
from .camera import Camera
from .lighting import LightingSystem, create_lighting_system
from utils.noise import generate_bark_texture, generate_leaf_texture

class PlantRenderer:
    """Main renderer for 3D plant visualization"""
    
    def __init__(self, width=1200, height=800):
        self.width = width
        self.height = height
        
        # ModernGL context (will be set by main application)
        self.ctx = None
        
        # Shader programs
        self.programs = {}
        
        # Textures
        self.textures = {}
        
        # Vertex buffers and arrays
        self.vaos = {}
        self.vbos = {}
        
        # Camera and lighting
        self.camera = Camera(target=[0, 1, 0], distance=3.0, 
                           aspect=width/height)
        self.lighting = create_lighting_system('sunny_day')
        
        # Rendering state
        self.wireframe_mode = False
        self.show_normals = False
        self.background_color = (0.5, 0.7, 1.0, 1.0)  # Sky blue
        
        # Animation
        self.time = 0.0
        
    def initialize(self, ctx):
        """Initialize renderer with ModernGL context"""
        self.ctx = ctx
        
        # Enable depth testing and face culling
        self.ctx.enable(mgl.DEPTH_TEST)
        self.ctx.enable(mgl.CULL_FACE)
        self.ctx.enable(mgl.BLEND)
        self.ctx.blend_func = mgl.SRC_ALPHA, mgl.ONE_MINUS_SRC_ALPHA
        
        # Create shader programs
        self._create_shaders()
        
        # Create textures
        self._create_textures()
        
        print("Renderer initialized successfully")
    
    def _create_shaders(self):
        """Create all shader programs"""
        for name, config in SHADER_CONFIGS.items():
            try:
                program = self.ctx.program(
                    vertex_shader=config['vertex'],
                    fragment_shader=config['fragment']
                )
                self.programs[name] = program
                print(f"Created shader program: {name}")
            except Exception as e:
                print(f"Failed to create shader {name}: {e}")
    
    def _create_textures(self):
        """Create textures for bark and leaves"""
        # Create bark texture
        bark_data = generate_bark_texture(256, 256, scale=0.05)
        bark_texture = self.ctx.texture((256, 256), 3, bark_data.tobytes())
        bark_texture.filter = (mgl.LINEAR_MIPMAP_LINEAR, mgl.LINEAR)
        bark_texture.build_mipmaps()
        self.textures['bark'] = bark_texture
        
        # Create leaf texture
        leaf_data = generate_leaf_texture(128, 128)
        leaf_texture = self.ctx.texture((128, 128), 4, leaf_data.tobytes())
        leaf_texture.filter = (mgl.LINEAR_MIPMAP_LINEAR, mgl.LINEAR)
        leaf_texture.build_mipmaps()
        self.textures['leaf'] = leaf_texture
        
        print("Textures created successfully")
    
    def update_geometry(self, plant):
        """Update geometry buffers from plant data"""
        # Update branch geometry
        branch_geom = plant.get_branch_geometry()
        if branch_geom[0] is not None:
            self._update_branch_buffers(branch_geom)
        
        # Update leaf geometry
        leaf_geom = plant.get_leaf_geometry()
        if leaf_geom[0] is not None:
            self._update_leaf_buffers(leaf_geom)
    
    def _update_branch_buffers(self, geometry):
        """Update branch vertex buffers"""
        vertices, normals, texcoords, indices = geometry
        
        # Interleave vertex data
        vertex_data = np.column_stack([vertices, normals, texcoords])
        vertex_data = vertex_data.astype(np.float32).tobytes()
        
        # Create or update VBO
        if 'branches' in self.vbos:
            # Update existing buffer if size matches
            if len(vertex_data) <= self.vbos['branches'].size:
                self.vbos['branches'].write(vertex_data)
            else:
                # Recreate buffer if larger
                self.vbos['branches'].release()
                self.vbos['branches'] = self.ctx.buffer(vertex_data)
        else:
            self.vbos['branches'] = self.ctx.buffer(vertex_data)
        
        # Create or update index buffer
        index_data = indices.astype(np.uint32).tobytes()
        if 'branch_indices' in self.vbos:
            if len(index_data) <= self.vbos['branch_indices'].size:
                self.vbos['branch_indices'].write(index_data)
            else:
                self.vbos['branch_indices'].release()
                self.vbos['branch_indices'] = self.ctx.buffer(index_data)
        else:
            self.vbos['branch_indices'] = self.ctx.buffer(index_data)
        
        # Create VAO
        if 'branches' in self.vaos:
            self.vaos['branches'].release()
        
        self.vaos['branches'] = self.ctx.vertex_array(
            self.programs['branch'],
            [(self.vbos['branches'], '3f 3f 2f', 'aPos', 'aNormal', 'aTexCoord')],
            self.vbos['branch_indices']
        )
        
        self.branch_index_count = len(indices)
    
    def _update_leaf_buffers(self, geometry):
        """Update leaf vertex buffers"""
        vertices, normals, texcoords, indices = geometry
        
        # Interleave vertex data
        vertex_data = np.column_stack([vertices, normals, texcoords])
        vertex_data = vertex_data.astype(np.float32).tobytes()
        
        # Create or update VBO
        if 'leaves' in self.vbos:
            if len(vertex_data) <= self.vbos['leaves'].size:
                self.vbos['leaves'].write(vertex_data)
            else:
                self.vbos['leaves'].release()
                self.vbos['leaves'] = self.ctx.buffer(vertex_data)
        else:
            self.vbos['leaves'] = self.ctx.buffer(vertex_data)
        
        # Create or update index buffer
        index_data = indices.astype(np.uint32).tobytes()
        if 'leaf_indices' in self.vbos:
            if len(index_data) <= self.vbos['leaf_indices'].size:
                self.vbos['leaf_indices'].write(index_data)
            else:
                self.vbos['leaf_indices'].release()
                self.vbos['leaf_indices'] = self.ctx.buffer(index_data)
        else:
            self.vbos['leaf_indices'] = self.ctx.buffer(index_data)
        
        # Create VAO
        if 'leaves' in self.vaos:
            self.vaos['leaves'].release()
        
        self.vaos['leaves'] = self.ctx.vertex_array(
            self.programs['leaf'],
            [(self.vbos['leaves'], '3f 3f 2f', 'aPos', 'aNormal', 'aTexCoord')],
            self.vbos['leaf_indices']
        )
        
        self.leaf_index_count = len(indices)
    
    def render(self, plant, dt):
        """Render the plant"""
        self.time += dt
        
        # Update lighting
        self.lighting.update(dt)
        
        # Clear screen
        self.ctx.clear(*self.background_color)
        
        # Set wireframe mode
        if self.wireframe_mode:
            self.ctx.wireframe = True
        else:
            self.ctx.wireframe = False
        
        # Update geometry from plant
        self.update_geometry(plant)
        
        # Render branches
        self._render_branches()
        
        # Render leaves
        self._render_leaves()
    
    def _render_branches(self):
        """Render branch geometry"""
        if 'branches' not in self.vaos or not hasattr(self, 'branch_index_count'):
            return
        
        program = self.programs['branch']
        program.use()
        
        # Set matrices
        model_matrix = np.eye(4, dtype=np.float32)
        view_matrix = self.camera.get_view_matrix().astype(np.float32)
        proj_matrix = self.camera.get_projection_matrix().astype(np.float32)
        normal_matrix = np.linalg.inv(model_matrix[:3, :3]).T.astype(np.float32)
        
        program['model'].write(model_matrix.tobytes())
        program['view'].write(view_matrix.tobytes())
        program['projection'].write(proj_matrix.tobytes())
        program['normalMatrix'].write(normal_matrix.tobytes())
        
        # Set lighting uniforms
        primary_light = self.lighting.get_primary_light()
        if primary_light:
            program['lightPos'].value = tuple(primary_light.get_position() if hasattr(primary_light, 'get_position') 
                                            else [0, 5, 0])
            program['lightColor'].value = tuple(primary_light.get_color())
        
        program['viewPos'].value = tuple(self.camera.get_position())
        
        # Set material properties
        program['ambientStrength'].value = 0.3
        program['specularStrength'].value = 0.1
        program['shininess'].value = 8
        
        # Bind bark texture
        self.textures['bark'].use(0)
        program['barkTexture'].value = 0
        
        # Render
        self.vaos['branches'].render()
    
    def _render_leaves(self):
        """Render leaf geometry"""
        if 'leaves' not in self.vaos or not hasattr(self, 'leaf_index_count'):
            return
        
        program = self.programs['leaf']
        program.use()
        
        # Set matrices
        model_matrix = np.eye(4, dtype=np.float32)
        view_matrix = self.camera.get_view_matrix().astype(np.float32)
        proj_matrix = self.camera.get_projection_matrix().astype(np.float32)
        normal_matrix = np.linalg.inv(model_matrix[:3, :3]).T.astype(np.float32)
        
        program['model'].write(model_matrix.tobytes())
        program['view'].write(view_matrix.tobytes())
        program['projection'].write(proj_matrix.tobytes())
        program['normalMatrix'].write(normal_matrix.tobytes())
        
        # Set time for animation
        program['time'].value = self.time
        
        # Set lighting uniforms
        primary_light = self.lighting.get_primary_light()
        if primary_light:
            program['lightPos'].value = tuple(primary_light.get_position() if hasattr(primary_light, 'get_position') 
                                            else [0, 5, 0])
            program['lightColor'].value = tuple(primary_light.get_color())
        
        program['viewPos'].value = tuple(self.camera.get_position())
        
        # Set material properties
        program['ambientStrength'].value = 0.4
        program['subsurfaceStrength'].value = 0.6
        program['leafColor'].value = (0.2, 0.7, 0.2)
        
        # Bind leaf texture
        self.textures['leaf'].use(0)
        program['leafTexture'].value = 0
        
        # Render with alpha blending
        self.ctx.enable(mgl.BLEND)
        self.vaos['leaves'].render()
    
    def resize(self, width, height):
        """Handle window resize"""
        self.width = width
        self.height = height
        self.camera.set_aspect_ratio(width / height)
        self.ctx.viewport = (0, 0, width, height)
    
    def cleanup(self):
        """Clean up resources"""
        for vao in self.vaos.values():
            vao.release()
        for vbo in self.vbos.values():
            vbo.release()
        for texture in self.textures.values():
            texture.release()
        for program in self.programs.values():
            program.release()
