"""
3D Camera system for plant visualization
"""
import numpy as np
import math
from utils.math_utils import look_at_matrix, perspective_matrix, normalize

class Camera:
    """3D camera with orbital controls"""
    
    def __init__(self, target=None, distance=3.0, fov=45.0, aspect=1.0, near=0.1, far=100.0):
        self.target = np.array(target) if target else np.array([0, 1, 0])
        self.distance = distance
        self.fov = math.radians(fov)
        self.aspect = aspect
        self.near = near
        self.far = far
        
        # Spherical coordinates for orbital camera
        self.theta = 0.0  # Horizontal angle (azimuth)
        self.phi = math.pi * 0.3  # Vertical angle (elevation)
        
        # Camera constraints
        self.min_distance = 0.5
        self.max_distance = 20.0
        self.min_phi = 0.1  # Prevent camera from going below ground
        self.max_phi = math.pi - 0.1
        
        # Movement parameters
        self.rotation_speed = 0.005
        self.zoom_speed = 0.1
        self.pan_speed = 0.001
        
        # Current matrices
        self.view_matrix = np.eye(4)
        self.projection_matrix = np.eye(4)
        self.position = np.array([0, 0, 0])
        
        self._update_matrices()
    
    def _update_matrices(self):
        """Update view and projection matrices"""
        # Calculate camera position from spherical coordinates
        x = self.distance * math.sin(self.phi) * math.cos(self.theta)
        y = self.distance * math.cos(self.phi)
        z = self.distance * math.sin(self.phi) * math.sin(self.theta)
        
        self.position = self.target + np.array([x, y, z])
        
        # Create view matrix
        up = np.array([0, 1, 0])
        self.view_matrix = look_at_matrix(self.position, self.target, up)
        
        # Create projection matrix
        self.projection_matrix = perspective_matrix(self.fov, self.aspect, self.near, self.far)
    
    def rotate(self, delta_x, delta_y):
        """Rotate camera around target"""
        self.theta += delta_x * self.rotation_speed
        self.phi += delta_y * self.rotation_speed
        
        # Constrain phi to prevent flipping
        self.phi = max(self.min_phi, min(self.max_phi, self.phi))
        
        # Normalize theta
        self.theta = self.theta % (2 * math.pi)
        
        self._update_matrices()
    
    def zoom(self, delta):
        """Zoom camera in/out"""
        self.distance += delta * self.zoom_speed
        self.distance = max(self.min_distance, min(self.max_distance, self.distance))
        
        self._update_matrices()
    
    def pan(self, delta_x, delta_y):
        """Pan camera target"""
        # Calculate camera's right and up vectors
        forward = normalize(self.target - self.position)
        right = normalize(np.cross(forward, np.array([0, 1, 0])))
        up = normalize(np.cross(right, forward))
        
        # Move target
        pan_distance = self.distance * self.pan_speed
        self.target += right * delta_x * pan_distance
        self.target += up * delta_y * pan_distance
        
        self._update_matrices()
    
    def set_target(self, target):
        """Set camera target position"""
        self.target = np.array(target)
        self._update_matrices()
    
    def set_distance(self, distance):
        """Set camera distance from target"""
        self.distance = max(self.min_distance, min(self.max_distance, distance))
        self._update_matrices()
    
    def set_aspect_ratio(self, aspect):
        """Update aspect ratio"""
        self.aspect = aspect
        self._update_matrices()
    
    def get_view_matrix(self):
        """Get current view matrix"""
        return self.view_matrix
    
    def get_projection_matrix(self):
        """Get current projection matrix"""
        return self.projection_matrix
    
    def get_position(self):
        """Get current camera position"""
        return self.position.copy()
    
    def get_direction(self):
        """Get camera forward direction"""
        return normalize(self.target - self.position)
    
    def reset(self):
        """Reset camera to default position"""
        self.theta = 0.0
        self.phi = math.pi * 0.3
        self.distance = 3.0
        self.target = np.array([0, 1, 0])
        self._update_matrices()

class FreeCamera:
    """Free-flying camera for debugging"""
    
    def __init__(self, position=None, fov=45.0, aspect=1.0, near=0.1, far=100.0):
        self.position = np.array(position) if position else np.array([0, 2, 3])
        self.fov = math.radians(fov)
        self.aspect = aspect
        self.near = near
        self.far = far
        
        # Orientation
        self.yaw = -90.0  # Horizontal rotation
        self.pitch = 0.0  # Vertical rotation
        
        # Movement
        self.movement_speed = 2.0
        self.mouse_sensitivity = 0.1
        
        # Vectors
        self.front = np.array([0, 0, -1])
        self.up = np.array([0, 1, 0])
        self.right = np.array([1, 0, 0])
        self.world_up = np.array([0, 1, 0])
        
        self._update_vectors()
    
    def _update_vectors(self):
        """Update camera vectors from yaw and pitch"""
        front = np.array([
            math.cos(math.radians(self.yaw)) * math.cos(math.radians(self.pitch)),
            math.sin(math.radians(self.pitch)),
            math.sin(math.radians(self.yaw)) * math.cos(math.radians(self.pitch))
        ])
        
        self.front = normalize(front)
        self.right = normalize(np.cross(self.front, self.world_up))
        self.up = normalize(np.cross(self.right, self.front))
    
    def move(self, direction, delta_time):
        """Move camera in specified direction"""
        velocity = self.movement_speed * delta_time
        
        if direction == 'forward':
            self.position += self.front * velocity
        elif direction == 'backward':
            self.position -= self.front * velocity
        elif direction == 'left':
            self.position -= self.right * velocity
        elif direction == 'right':
            self.position += self.right * velocity
        elif direction == 'up':
            self.position += self.world_up * velocity
        elif direction == 'down':
            self.position -= self.world_up * velocity
    
    def rotate(self, delta_x, delta_y):
        """Rotate camera based on mouse movement"""
        self.yaw += delta_x * self.mouse_sensitivity
        self.pitch += delta_y * self.mouse_sensitivity
        
        # Constrain pitch
        self.pitch = max(-89.0, min(89.0, self.pitch))
        
        self._update_vectors()
    
    def get_view_matrix(self):
        """Get view matrix"""
        return look_at_matrix(self.position, self.position + self.front, self.up)
    
    def get_projection_matrix(self):
        """Get projection matrix"""
        return perspective_matrix(self.fov, self.aspect, self.near, self.far)
    
    def get_position(self):
        """Get camera position"""
        return self.position.copy()
    
    def set_aspect_ratio(self, aspect):
        """Update aspect ratio"""
        self.aspect = aspect
