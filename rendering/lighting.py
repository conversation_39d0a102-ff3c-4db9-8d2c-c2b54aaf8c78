"""
Lighting system for realistic plant rendering
"""
import numpy as np
import math
from utils.math_utils import normalize, spherical_to_cartesian

class Light:
    """Base light class"""
    
    def __init__(self, color=None, intensity=1.0):
        self.color = np.array(color) if color else np.array([1.0, 1.0, 1.0])
        self.intensity = intensity
        self.enabled = True
    
    def get_color(self):
        """Get light color with intensity"""
        return self.color * self.intensity if self.enabled else np.array([0, 0, 0])

class DirectionalLight(Light):
    """Directional light (like sunlight)"""
    
    def __init__(self, direction=None, color=None, intensity=1.0):
        super().__init__(color, intensity)
        self.direction = normalize(np.array(direction)) if direction else np.array([0, -1, 0])
    
    def set_direction(self, direction):
        """Set light direction"""
        self.direction = normalize(np.array(direction))
    
    def get_direction(self):
        """Get light direction"""
        return self.direction.copy()

class PointLight(Light):
    """Point light source"""
    
    def __init__(self, position=None, color=None, intensity=1.0, 
                 constant=1.0, linear=0.09, quadratic=0.032):
        super().__init__(color, intensity)
        self.position = np.array(position) if position else np.array([0, 5, 0])
        
        # Attenuation parameters
        self.constant = constant
        self.linear = linear
        self.quadratic = quadratic
    
    def set_position(self, position):
        """Set light position"""
        self.position = np.array(position)
    
    def get_position(self):
        """Get light position"""
        return self.position.copy()
    
    def calculate_attenuation(self, distance):
        """Calculate light attenuation at given distance"""
        return 1.0 / (self.constant + self.linear * distance + 
                     self.quadratic * distance * distance)

class SunLight(DirectionalLight):
    """Animated sun light that moves across the sky"""
    
    def __init__(self, color=None, intensity=1.0):
        # Start with sun at noon position
        super().__init__(direction=[0, -1, 0.3], color=color, intensity=intensity)
        
        # Sun animation parameters
        self.time = 0.0
        self.day_length = 60.0  # Seconds for full day cycle
        self.sun_path_radius = 1.0
        self.sun_height = 0.8
        
        # Color temperature changes
        self.noon_color = np.array([1.0, 1.0, 0.9])      # Bright white
        self.sunrise_color = np.array([1.0, 0.7, 0.4])   # Orange
        self.sunset_color = np.array([1.0, 0.5, 0.3])    # Red-orange
        
    def update(self, dt):
        """Update sun position and color based on time"""
        self.time += dt
        
        # Calculate sun position (0 = midnight, 0.5 = noon)
        day_progress = (self.time % self.day_length) / self.day_length
        sun_angle = day_progress * 2 * math.pi
        
        # Sun moves in an arc across the sky
        x = math.sin(sun_angle) * self.sun_path_radius
        y = -abs(math.cos(sun_angle)) * self.sun_height  # Always pointing down
        z = math.cos(sun_angle) * 0.3  # Slight forward bias
        
        self.direction = normalize(np.array([x, y, z]))
        
        # Update color based on sun height
        sun_height_factor = abs(y)  # 0 = horizon, 1 = zenith
        
        if sun_height_factor > 0.7:  # High sun (noon)
            self.color = self.noon_color
            self.intensity = 1.0
        elif sun_height_factor > 0.3:  # Medium sun
            # Interpolate between sunrise and noon
            t = (sun_height_factor - 0.3) / 0.4
            self.color = self.sunrise_color * (1 - t) + self.noon_color * t
            self.intensity = 0.7 + 0.3 * t
        else:  # Low sun (sunrise/sunset)
            self.color = self.sunrise_color if x > 0 else self.sunset_color
            self.intensity = 0.3 + 0.4 * sun_height_factor
    
    def set_time_of_day(self, time_factor):
        """Set time of day (0.0 = midnight, 0.5 = noon, 1.0 = midnight)"""
        self.time = time_factor * self.day_length

class LightingSystem:
    """Manages multiple lights and calculates combined lighting"""
    
    def __init__(self):
        self.lights = []
        self.ambient_color = np.array([0.2, 0.2, 0.3])  # Slight blue ambient
        self.ambient_intensity = 0.3
        
        # Add default sun light
        self.sun = SunLight(color=[1.0, 0.95, 0.8], intensity=1.0)
        self.add_light(self.sun)
    
    def add_light(self, light):
        """Add a light to the system"""
        self.lights.append(light)
    
    def remove_light(self, light):
        """Remove a light from the system"""
        if light in self.lights:
            self.lights.remove(light)
    
    def update(self, dt):
        """Update all lights"""
        for light in self.lights:
            if hasattr(light, 'update'):
                light.update(dt)
    
    def get_primary_light(self):
        """Get the primary light source (usually the sun)"""
        return self.sun if self.sun in self.lights else None
    
    def get_ambient_light(self):
        """Get ambient light color"""
        return self.ambient_color * self.ambient_intensity
    
    def set_ambient(self, color, intensity=None):
        """Set ambient lighting"""
        self.ambient_color = np.array(color)
        if intensity is not None:
            self.ambient_intensity = intensity
    
    def set_time_of_day(self, time_factor):
        """Set time of day for sun light"""
        if self.sun:
            self.sun.set_time_of_day(time_factor)
    
    def get_lighting_uniforms(self):
        """Get lighting uniforms for shaders"""
        primary = self.get_primary_light()
        
        uniforms = {
            'ambientColor': self.get_ambient_light(),
            'ambientStrength': self.ambient_intensity
        }
        
        if primary:
            if isinstance(primary, DirectionalLight):
                uniforms.update({
                    'lightType': 0,  # Directional
                    'lightDirection': primary.get_direction(),
                    'lightColor': primary.get_color()
                })
            elif isinstance(primary, PointLight):
                uniforms.update({
                    'lightType': 1,  # Point
                    'lightPos': primary.get_position(),
                    'lightColor': primary.get_color(),
                    'lightConstant': primary.constant,
                    'lightLinear': primary.linear,
                    'lightQuadratic': primary.quadratic
                })
        
        return uniforms

# Preset lighting configurations
LIGHTING_PRESETS = {
    'sunny_day': {
        'sun_color': [1.0, 0.95, 0.8],
        'sun_intensity': 1.2,
        'ambient_color': [0.3, 0.3, 0.4],
        'ambient_intensity': 0.4
    },
    'overcast': {
        'sun_color': [0.8, 0.8, 0.9],
        'sun_intensity': 0.6,
        'ambient_color': [0.4, 0.4, 0.5],
        'ambient_intensity': 0.6
    },
    'golden_hour': {
        'sun_color': [1.0, 0.7, 0.4],
        'sun_intensity': 0.8,
        'ambient_color': [0.4, 0.3, 0.2],
        'ambient_intensity': 0.3
    },
    'indoor': {
        'sun_color': [1.0, 1.0, 1.0],
        'sun_intensity': 0.5,
        'ambient_color': [0.5, 0.5, 0.5],
        'ambient_intensity': 0.7
    }
}

def create_lighting_system(preset='sunny_day'):
    """Create a lighting system with a preset configuration"""
    system = LightingSystem()
    
    if preset in LIGHTING_PRESETS:
        config = LIGHTING_PRESETS[preset]
        system.sun.color = np.array(config['sun_color'])
        system.sun.intensity = config['sun_intensity']
        system.set_ambient(config['ambient_color'], config['ambient_intensity'])
    
    return system
