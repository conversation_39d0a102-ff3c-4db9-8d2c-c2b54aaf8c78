"""
Safe version of 3D Plant Growth Simulation with better error handling
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys
import traceback

from plant_system.plant import Plant
from rendering.renderer import PlantRenderer

class SafePlantSimulation:
    """Safer version of plant simulation with extensive error handling"""
    
    def __init__(self, width=800, height=600):
        self.width = width
        self.height = height
        self.running = True
        self.initialized = False
        
        print("Initializing Safe Plant Simulation...")
        
        try:
            self._init_pygame()
            self._init_opengl()
            self._init_renderer()
            self._init_plant()
            self._init_controls()
            self.initialized = True
            print("✓ Simulation initialized successfully!")
            
        except Exception as e:
            print(f"✗ Initialization failed: {e}")
            traceback.print_exc()
            self.cleanup()
            raise
    
    def _init_pygame(self):
        """Initialize Pygame with error handling"""
        print("Initializing Pygame...")
        pygame.init()
        
        # Set OpenGL attributes
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        pygame.display.gl_set_attribute(pygame.GL_DEPTH_SIZE, 24)
        
        # Create display
        flags = pygame.OPENGL | pygame.DOUBLEBUF
        self.screen = pygame.display.set_mode((self.width, self.height), flags)
        pygame.display.set_caption("3D Plant Growth Simulation (Safe Mode)")
        
        print("✓ Pygame initialized")
    
    def _init_opengl(self):
        """Initialize OpenGL context"""
        print("Creating OpenGL context...")
        self.ctx = mgl.create_context()
        
        info = self.ctx.info
        print(f"✓ OpenGL {info['GL_VERSION']}")
        print(f"✓ GPU: {info['GL_RENDERER']}")
        print(f"✓ Vendor: {info['GL_VENDOR']}")
    
    def _init_renderer(self):
        """Initialize renderer"""
        print("Initializing renderer...")
        self.renderer = PlantRenderer(self.width, self.height)
        self.renderer.initialize(self.ctx)
        print("✓ Renderer initialized")
    
    def _init_plant(self):
        """Initialize plant"""
        print("Creating plant...")
        self.plant = Plant(plant_type='simple_tree', position=[0, 0, 0])
        print("✓ Plant created")
    
    def _init_controls(self):
        """Initialize control state"""
        self.keys = {}
        self.mouse_buttons = [False, False, False]
        self.paused = False
        self.growth_speed = 1.0
        self.show_stats = True
        self.clock = pygame.time.Clock()
        self.fps_target = 60
        print("✓ Controls initialized")
    
    def handle_events(self):
        """Handle input events with error handling"""
        try:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                
                elif event.type == pygame.KEYDOWN:
                    self.keys[event.key] = True
                    
                    if event.key == pygame.K_ESCAPE:
                        self.running = False
                    elif event.key == pygame.K_SPACE:
                        self.paused = not self.paused
                        print(f"Simulation {'paused' if self.paused else 'resumed'}")
                    elif event.key == pygame.K_r:
                        self.plant.reset()
                        print("Plant reset")
                    elif event.key == pygame.K_f:
                        self.renderer.wireframe_mode = not self.renderer.wireframe_mode
                        print(f"Wireframe mode: {self.renderer.wireframe_mode}")
                    elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                        self.growth_speed = min(5.0, self.growth_speed * 1.2)
                        self.plant.set_growth_speed(self.growth_speed)
                        print(f"Growth speed: {self.growth_speed:.1f}x")
                    elif event.key == pygame.K_MINUS:
                        self.growth_speed = max(0.1, self.growth_speed / 1.2)
                        self.plant.set_growth_speed(self.growth_speed)
                        print(f"Growth speed: {self.growth_speed:.1f}x")
                
                elif event.type == pygame.KEYUP:
                    self.keys[event.key] = False
                
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    self.mouse_buttons[event.button - 1] = True
                
                elif event.type == pygame.MOUSEBUTTONUP:
                    self.mouse_buttons[event.button - 1] = False
                
                elif event.type == pygame.MOUSEWHEEL:
                    self.renderer.camera.zoom(event.y * -0.5)
                
                elif event.type == pygame.MOUSEMOTION:
                    if self.mouse_buttons[0]:  # Left mouse button
                        dx = event.rel[0]
                        dy = event.rel[1]
                        self.renderer.camera.rotate(dx, dy)
                    elif self.mouse_buttons[2]:  # Right mouse button
                        dx = event.rel[0]
                        dy = event.rel[1]
                        self.renderer.camera.pan(-dx, dy)
                
                elif event.type == pygame.VIDEORESIZE:
                    self.width = event.w
                    self.height = event.h
                    self.renderer.resize(self.width, self.height)
        
        except Exception as e:
            print(f"Error in event handling: {e}")
    
    def update(self, dt):
        """Update simulation with error handling"""
        try:
            if not self.paused:
                self.plant.update(dt * self.growth_speed)
        except Exception as e:
            print(f"Error in plant update: {e}")
    
    def render(self):
        """Render with error handling"""
        try:
            self.renderer.render(self.plant, self.dt)
            pygame.display.flip()
        except Exception as e:
            print(f"Error in rendering: {e}")
            # Try to clear and continue
            try:
                self.ctx.clear(0.5, 0.7, 1.0, 1.0)
                pygame.display.flip()
            except:
                pass
    
    def run(self):
        """Main application loop with error handling"""
        if not self.initialized:
            print("Cannot run - initialization failed")
            return 1
        
        print("Starting plant growth simulation...")
        print("\nControls:")
        print("  Mouse: Rotate camera")
        print("  Mouse wheel: Zoom")
        print("  Space: Pause/Resume")
        print("  R: Reset plant")
        print("  +/-: Adjust growth speed")
        print("  F: Toggle wireframe")
        print("  ESC: Exit")
        print()
        
        frame_count = 0
        last_stats_time = time.time()
        
        try:
            while self.running:
                # Calculate delta time
                self.dt = self.clock.tick(self.fps_target) / 1000.0
                frame_count += 1
                
                # Handle events
                self.handle_events()
                
                # Update simulation
                self.update(self.dt)
                
                # Render
                self.render()
                
                # Print stats every 5 seconds
                current_time = time.time()
                if current_time - last_stats_time >= 5.0:
                    stats = self.plant.get_stats()
                    fps = self.clock.get_fps()
                    print(f"Age: {stats['age']:.1f}s, Branches: {stats['branches']}, "
                          f"Iteration: {stats['iteration']}, FPS: {fps:.1f}")
                    last_stats_time = current_time
        
        except KeyboardInterrupt:
            print("\nSimulation interrupted by user")
        except Exception as e:
            print(f"\nSimulation error: {e}")
            traceback.print_exc()
            return 1
        finally:
            self.cleanup()
        
        print("Simulation ended normally")
        return 0
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'renderer'):
                self.renderer.cleanup()
            pygame.quit()
            print("✓ Cleanup completed")
        except Exception as e:
            print(f"Error during cleanup: {e}")

def main():
    """Main entry point with comprehensive error handling"""
    try:
        # Create and run simulation
        simulation = SafePlantSimulation(800, 600)
        return simulation.run()
        
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
