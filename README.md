# 3D Plant Growth Simulation

A realistic 3D plant growth simulation using L-Systems (Lindenmayer Systems) and modern OpenGL rendering, optimized for RX 580 GPU performance.

## Features

### 🌱 Realistic Plant Growth
- **L-System Implementation**: Procedural branching patterns using mathematical rules
- **Phototropism**: Plants grow toward light sources
- **Gravitropism**: Natural upward growth bias
- **Apical Dominance**: Main stem growth takes priority
- **Resource Allocation**: Realistic distribution of growth energy

### 🎨 Advanced 3D Rendering
- **ModernGL**: High-performance OpenGL wrapper optimized for RX 580
- **Phong Lighting**: Realistic lighting with ambient, diffuse, and specular components
- **Procedural Textures**: Bark and leaf textures generated with Perlin noise
- **Subsurface Scattering**: Organic light transmission through leaves
- **Real-time Shadows**: Dynamic lighting effects

### 🎮 Interactive Controls
- **Orbital Camera**: Mouse-controlled 3D camera with zoom and pan
- **Growth Speed Control**: Speed up or slow down the simulation
- **Light Manipulation**: Move light sources to influence growth
- **Reset/Restart**: Start over with new growth patterns
- **Wireframe Mode**: Debug view of 3D geometry

### 🔬 Scientific Accuracy
- **Differential Growth Rates**: Branches grow at different speeds
- **Secondary Growth**: Branches thicken over time
- **Leaf Lifecycle**: Leaves grow, mature, age, and eventually fall
- **Environmental Response**: Growth adapts to light conditions

## Installation

### Prerequisites
- Python 3.7 or higher
- OpenGL 3.3+ compatible graphics card (RX 580 recommended)
- Windows/Linux/macOS

### Quick Setup
```bash
# Clone or download the project
cd plant-simulation

# Run setup script
python setup.py

# Start the simulation
python main.py
```

### Manual Installation
```bash
pip install -r requirements.txt
python main.py
```

## Controls

### Camera
- **Mouse Drag**: Rotate camera around plant
- **Mouse Wheel**: Zoom in/out
- **WASD**: Pan camera
- **Right Mouse + Drag**: Pan camera

### Simulation
- **Space**: Pause/Resume growth
- **R**: Reset plant to seed
- **+/-**: Increase/decrease growth speed
- **Arrow Keys**: Move light source

### Display
- **F**: Toggle wireframe mode
- **H**: Toggle help/statistics
- **ESC**: Exit simulation

## Technical Details

### L-System Rules
The simulation uses context-free L-Systems with rules like:
- `F` → `F[+F][-F]` (branch with two sub-branches)
- `F` → `F[&+F][&-F][^+F][^-F]L` (3D branching with leaves)

### Growth Mechanics
1. **Turtle Graphics**: 3D interpretation of L-System strings
2. **Environmental Forces**: Light attraction and gravity response
3. **Resource Competition**: Limited energy distribution
4. **Stochastic Elements**: Random variations for natural appearance

### Rendering Pipeline
1. **Geometry Generation**: Procedural cylinder creation for branches
2. **Texture Mapping**: Perlin noise-based bark and leaf textures
3. **Lighting Calculation**: Phong shading with multiple light sources
4. **Alpha Blending**: Transparent leaves with proper depth sorting

## Performance Optimization

### RX 580 Specific
- **Vertex Buffer Optimization**: Efficient GPU memory usage
- **Instanced Rendering**: Reduced draw calls for leaves
- **Texture Compression**: Optimized texture formats
- **LOD System**: Distance-based detail reduction

### General Optimizations
- **Frustum Culling**: Only render visible geometry
- **Occlusion Culling**: Skip hidden branches
- **Temporal Coherence**: Reuse calculations between frames
- **Memory Pooling**: Efficient object allocation

## Customization

### Plant Types
Modify `plant_system/lsystem.py` to create new plant species:
```python
PLANT_RULES = {
    'my_plant': {
        'axiom': 'F',
        'rules': {'F': 'F[+F][-F]F'},
        'angle': 30.0
    }
}
```

### Visual Settings
Adjust rendering parameters in `rendering/shaders.py`:
- Lighting intensity
- Material properties
- Color schemes
- Animation parameters

### Growth Parameters
Tune growth behavior in `plant_system/plant.py`:
- Growth speed
- Branching probability
- Resource allocation
- Environmental sensitivity

## Troubleshooting

### Common Issues

**Low FPS on RX 580**
- Reduce window size
- Lower texture resolution
- Disable wireframe mode
- Check GPU drivers

**Plant Not Growing**
- Check growth speed setting (+/- keys)
- Ensure simulation isn't paused (Space)
- Verify light source position

**Graphics Errors**
- Update graphics drivers
- Check OpenGL version (3.3+ required)
- Try running as administrator

### Debug Mode
Enable debug output by setting environment variable:
```bash
export PLANT_DEBUG=1
python main.py
```

## Scientific Background

### L-Systems
Developed by Aristid Lindenmayer in 1968, L-Systems are parallel rewriting systems used to model the growth processes of plants. They use simple rules to generate complex, self-similar structures.

### Plant Biology
The simulation models several key biological processes:
- **Phototropism**: Growth response to light direction
- **Gravitropism**: Growth response to gravity
- **Apical Dominance**: Hormonal control of branching
- **Resource Allocation**: Competition for nutrients and energy

## Contributing

### Code Structure
```
plant-simulation/
├── plant_system/          # Core growth simulation
│   ├── lsystem.py        # L-System implementation
│   ├── plant.py          # Main plant class
│   ├── branch.py         # Branch mechanics
│   └── leaf.py           # Leaf behavior
├── rendering/            # 3D graphics
│   ├── renderer.py       # Main renderer
│   ├── shaders.py        # GLSL shaders
│   ├── camera.py         # Camera system
│   └── lighting.py       # Lighting system
├── utils/                # Utilities
│   ├── math_utils.py     # 3D mathematics
│   ├── noise.py          # Perlin noise
│   └── geometry.py       # Mesh generation
└── main.py               # Application entry point
```

### Adding Features
1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is released under the MIT License. See LICENSE file for details.

## Acknowledgments

- Aristid Lindenmayer for L-System theory
- ModernGL team for excellent OpenGL bindings
- Pygame community for windowing and input
- Scientific visualization community for inspiration

## Future Enhancements

- **Seasonal Changes**: Automatic color transitions
- **Weather Effects**: Wind, rain, snow simulation
- **Ecosystem Simulation**: Multiple interacting plants
- **VR Support**: Immersive plant exploration
- **Machine Learning**: AI-driven growth optimization
- **Export Features**: 3D model export for 3D printing
