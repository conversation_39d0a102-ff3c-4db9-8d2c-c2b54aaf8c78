#!/usr/bin/env python3
"""
Quick test of plant growth simulation
"""
from plant_system.plant import Plant

print('=== Plant Growth Test ===')
plant = Plant(plant_type='realistic_tree')
print(f'Initial: {plant.get_stats()}')

# Simulate 6 seconds of growth (should trigger first L-System iteration)
for i in range(60):
    plant.update(0.1)  # 0.1 second updates
    if i % 10 == 0:  # Print every second
        stats = plant.get_stats()
        age = stats['age']
        branches = stats['branches']
        iteration = stats['iteration']
        print(f'Time {i/10:.1f}s: Age={age:.1f}, Branches={branches}, Iteration={iteration}')

print('✓ Plant growth simulation completed successfully!')

# Test geometry generation
branch_geom = plant.get_branch_geometry()
if branch_geom[0] is not None:
    vertices, normals, texcoords, indices = branch_geom
    print(f'✓ Branch geometry: {len(vertices)} vertices, {len(indices)} indices')
else:
    print('⚠ No branch geometry generated yet')

leaf_geom = plant.get_leaf_geometry()
if leaf_geom[0] is not None:
    vertices, normals, texcoords, indices = leaf_geom
    print(f'✓ Leaf geometry: {len(vertices)} vertices, {len(indices)} indices')
else:
    print('⚠ No leaf geometry generated yet')
