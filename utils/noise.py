"""
Perlin noise implementation for natural textures and variations
"""
import numpy as np
import noise

class PerlinNoise:
    """Wrapper for Perlin noise with convenient methods"""
    
    def __init__(self, octaves=4, persistence=0.5, lacunarity=2.0, seed=None):
        self.octaves = octaves
        self.persistence = persistence
        self.lacunarity = lacunarity
        self.seed = seed or np.random.randint(0, 1000000)
    
    def noise_1d(self, x, scale=1.0):
        """Generate 1D Perlin noise"""
        return noise.pnoise1(x * scale, 
                           octaves=self.octaves,
                           persistence=self.persistence,
                           lacunarity=self.lacunarity,
                           base=self.seed)
    
    def noise_2d(self, x, y, scale=1.0):
        """Generate 2D Perlin noise"""
        return noise.pnoise2(x * scale, y * scale,
                           octaves=self.octaves,
                           persistence=self.persistence,
                           lacunarity=self.lacunarity,
                           base=self.seed)
    
    def noise_3d(self, x, y, z, scale=1.0):
        """Generate 3D Perlin noise"""
        return noise.pnoise3(x * scale, y * scale, z * scale,
                           octaves=self.octaves,
                           persistence=self.persistence,
                           lacunarity=self.lacunarity,
                           base=self.seed)
    
    def fbm_1d(self, x, scale=1.0, amplitude=1.0):
        """Fractional Brownian Motion in 1D"""
        value = 0.0
        freq = scale
        amp = amplitude
        
        for _ in range(self.octaves):
            value += self.noise_1d(x, freq) * amp
            freq *= self.lacunarity
            amp *= self.persistence
        
        return value
    
    def fbm_2d(self, x, y, scale=1.0, amplitude=1.0):
        """Fractional Brownian Motion in 2D"""
        value = 0.0
        freq = scale
        amp = amplitude
        
        for _ in range(self.octaves):
            value += self.noise_2d(x, y, freq) * amp
            freq *= self.lacunarity
            amp *= self.persistence
        
        return value
    
    def fbm_3d(self, x, y, z, scale=1.0, amplitude=1.0):
        """Fractional Brownian Motion in 3D"""
        value = 0.0
        freq = scale
        amp = amplitude
        
        for _ in range(self.octaves):
            value += self.noise_3d(x, y, z, freq) * amp
            freq *= self.lacunarity
            amp *= self.persistence
        
        return value

def generate_bark_texture(width, height, scale=0.1):
    """Generate bark-like texture using Perlin noise"""
    texture = np.zeros((height, width, 3), dtype=np.uint8)
    noise_gen = PerlinNoise(octaves=6, persistence=0.6, lacunarity=2.0)
    
    for y in range(height):
        for x in range(width):
            # Base bark color
            base_color = np.array([101, 67, 33])  # Brown
            
            # Add noise for texture variation
            noise_val = noise_gen.fbm_2d(x, y, scale)
            noise_val = (noise_val + 1) * 0.5  # Normalize to 0-1
            
            # Vary the color based on noise
            color_variation = noise_val * 50 - 25
            final_color = np.clip(base_color + color_variation, 0, 255)
            
            texture[y, x] = final_color.astype(np.uint8)
    
    return texture

def generate_leaf_texture(width, height):
    """Generate leaf-like texture"""
    texture = np.zeros((height, width, 4), dtype=np.uint8)  # RGBA
    noise_gen = PerlinNoise(octaves=4, persistence=0.5)
    
    center_x, center_y = width // 2, height // 2
    
    for y in range(height):
        for x in range(width):
            # Distance from center for leaf shape
            dx = (x - center_x) / (width * 0.5)
            dy = (y - center_y) / (height * 0.5)
            dist = np.sqrt(dx*dx + dy*dy)
            
            # Leaf shape (elliptical)
            leaf_shape = 1.0 - smoothstep(0.7, 1.0, dist)
            
            # Base green color
            base_color = np.array([34, 139, 34])  # Forest green
            
            # Add noise for natural variation
            noise_val = noise_gen.fbm_2d(x * 0.1, y * 0.1)
            color_variation = noise_val * 30
            
            # Vein pattern
            vein_noise = noise_gen.noise_2d(x * 0.05, y * 0.05)
            if abs(vein_noise) > 0.3:
                color_variation += 20
            
            final_color = np.clip(base_color + color_variation, 0, 255)
            alpha = int(leaf_shape * 255)
            
            texture[y, x] = [final_color[0], final_color[1], final_color[2], alpha]
    
    return texture

def smoothstep(edge0, edge1, x):
    """Smooth interpolation function"""
    t = np.clip((x - edge0) / (edge1 - edge0), 0.0, 1.0)
    return t * t * (3.0 - 2.0 * t)
