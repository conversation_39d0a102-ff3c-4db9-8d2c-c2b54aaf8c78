"""
3D Mathematics utilities for plant simulation
"""
import numpy as np
import math

def normalize(vector):
    """Normalize a vector to unit length"""
    norm = np.linalg.norm(vector)
    if norm == 0:
        return vector
    return vector / norm

def rotation_matrix_x(angle):
    """Create rotation matrix around X axis"""
    c, s = np.cos(angle), np.sin(angle)
    return np.array([
        [1, 0, 0],
        [0, c, -s],
        [0, s, c]
    ])

def rotation_matrix_y(angle):
    """Create rotation matrix around Y axis"""
    c, s = np.cos(angle), np.sin(angle)
    return np.array([
        [c, 0, s],
        [0, 1, 0],
        [-s, 0, c]
    ])

def rotation_matrix_z(angle):
    """Create rotation matrix around Z axis"""
    c, s = np.cos(angle), np.sin(angle)
    return np.array([
        [c, -s, 0],
        [s, c, 0],
        [0, 0, 1]
    ])

def rotation_matrix_axis_angle(axis, angle):
    """Create rotation matrix around arbitrary axis"""
    axis = normalize(axis)
    c = np.cos(angle)
    s = np.sin(angle)
    t = 1 - c
    x, y, z = axis
    
    return np.array([
        [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
        [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
        [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
    ])

def look_at_matrix(eye, target, up):
    """Create a look-at view matrix"""
    forward = normalize(target - eye)
    right = normalize(np.cross(forward, up))
    up = np.cross(right, forward)
    
    view = np.eye(4)
    view[0, :3] = right
    view[1, :3] = up
    view[2, :3] = -forward
    view[:3, 3] = -np.array([np.dot(right, eye), np.dot(up, eye), np.dot(forward, eye)])
    
    return view

def perspective_matrix(fov, aspect, near, far):
    """Create perspective projection matrix"""
    f = 1.0 / np.tan(fov / 2.0)
    proj = np.zeros((4, 4))
    proj[0, 0] = f / aspect
    proj[1, 1] = f
    proj[2, 2] = (far + near) / (near - far)
    proj[2, 3] = (2 * far * near) / (near - far)
    proj[3, 2] = -1
    return proj

def lerp(a, b, t):
    """Linear interpolation between a and b"""
    return a + t * (b - a)

def smoothstep(edge0, edge1, x):
    """Smooth interpolation function"""
    t = np.clip((x - edge0) / (edge1 - edge0), 0.0, 1.0)
    return t * t * (3.0 - 2.0 * t)

def random_unit_vector():
    """Generate a random unit vector"""
    theta = np.random.uniform(0, 2 * np.pi)
    phi = np.random.uniform(0, np.pi)
    x = np.sin(phi) * np.cos(theta)
    y = np.sin(phi) * np.sin(theta)
    z = np.cos(phi)
    return np.array([x, y, z])

def spherical_to_cartesian(radius, theta, phi):
    """Convert spherical coordinates to cartesian"""
    x = radius * np.sin(phi) * np.cos(theta)
    y = radius * np.sin(phi) * np.sin(theta)
    z = radius * np.cos(phi)
    return np.array([x, y, z])

def cartesian_to_spherical(x, y, z):
    """Convert cartesian coordinates to spherical"""
    radius = np.sqrt(x*x + y*y + z*z)
    theta = np.arctan2(y, x)
    phi = np.arccos(z / radius) if radius > 0 else 0
    return radius, theta, phi
