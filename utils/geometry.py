"""
Procedural geometry generation for plant components
"""
import numpy as np
import math
from .math_utils import normalize, rotation_matrix_axis_angle
from .noise import PerlinNoise

def generate_cylinder(radius_start, radius_end, length, segments=8, rings=4):
    """
    Generate cylindrical geometry with tapering
    Returns vertices, normals, texture coordinates, and indices
    """
    vertices = []
    normals = []
    texcoords = []
    indices = []
    
    # Generate vertices along the cylinder
    for ring in range(rings + 1):
        t = ring / rings  # Parameter along length
        y = t * length
        radius = radius_start + t * (radius_end - radius_start)
        
        for seg in range(segments):
            angle = 2 * math.pi * seg / segments
            x = radius * math.cos(angle)
            z = radius * math.sin(angle)
            
            vertices.append([x, y, z])
            
            # Normal vector (pointing outward)
            normal = normalize(np.array([x, 0, z]))
            normals.append(normal)
            
            # Texture coordinates
            u = seg / segments
            v = t
            texcoords.append([u, v])
    
    # Generate indices for triangles
    for ring in range(rings):
        for seg in range(segments):
            # Current ring indices
            curr = ring * segments + seg
            next_seg = ring * segments + (seg + 1) % segments
            
            # Next ring indices
            next_ring = (ring + 1) * segments + seg
            next_ring_next = (ring + 1) * segments + (seg + 1) % segments
            
            # Two triangles per quad
            indices.extend([curr, next_ring, next_seg])
            indices.extend([next_seg, next_ring, next_ring_next])
    
    return np.array(vertices), np.array(normals), np.array(texcoords), np.array(indices)

def generate_branch_geometry(start_pos, end_pos, start_radius, end_radius, segments=8):
    """Generate geometry for a single branch segment"""
    direction = normalize(end_pos - start_pos)
    length = np.linalg.norm(end_pos - start_pos)
    
    # Create local coordinate system
    up = np.array([0, 1, 0])
    if abs(np.dot(direction, up)) > 0.9:
        up = np.array([1, 0, 0])
    
    right = normalize(np.cross(direction, up))
    up = normalize(np.cross(right, direction))
    
    vertices = []
    normals = []
    texcoords = []
    indices = []
    
    # Add some natural irregularity
    noise_gen = PerlinNoise(octaves=3, persistence=0.3)
    
    rings = max(2, int(length * 10))  # More rings for longer branches
    
    for ring in range(rings + 1):
        t = ring / rings
        pos = start_pos + t * (end_pos - start_pos)
        radius = start_radius + t * (end_radius - start_radius)
        
        # Add slight irregularity
        noise_scale = 0.1
        radius_variation = 1.0 + noise_gen.noise_1d(t * 10) * noise_scale
        radius *= radius_variation
        
        for seg in range(segments):
            angle = 2 * math.pi * seg / segments
            
            # Add slight angular noise for natural look
            angle_noise = noise_gen.noise_2d(t * 5, seg) * 0.1
            angle += angle_noise
            
            # Local position on circle
            local_x = radius * math.cos(angle)
            local_z = radius * math.sin(angle)
            
            # Transform to world space
            world_pos = pos + local_x * right + local_z * up
            vertices.append(world_pos)
            
            # Normal vector
            normal = normalize(local_x * right + local_z * up)
            normals.append(normal)
            
            # Texture coordinates
            u = seg / segments
            v = t
            texcoords.append([u, v])
    
    # Generate indices
    for ring in range(rings):
        for seg in range(segments):
            curr = ring * segments + seg
            next_seg = ring * segments + (seg + 1) % segments
            next_ring = (ring + 1) * segments + seg
            next_ring_next = (ring + 1) * segments + (seg + 1) % segments
            
            indices.extend([curr, next_ring, next_seg])
            indices.extend([next_seg, next_ring, next_ring_next])
    
    return np.array(vertices), np.array(normals), np.array(texcoords), np.array(indices)

def generate_leaf_geometry(position, normal, size=0.1):
    """Generate leaf geometry as a textured quad"""
    # Create local coordinate system for the leaf
    up = normalize(normal)
    right = normalize(np.cross(up, np.array([0, 1, 0])))
    if np.linalg.norm(right) < 0.1:
        right = normalize(np.cross(up, np.array([1, 0, 0])))
    forward = normalize(np.cross(right, up))
    
    # Leaf shape (slightly curved)
    vertices = []
    normals = []
    texcoords = []
    
    # Simple quad for now, can be made more complex
    half_size = size * 0.5
    
    # Leaf vertices (slightly curved)
    leaf_verts = [
        [-half_size, 0, -half_size * 1.5],  # Bottom left
        [half_size, 0, -half_size * 1.5],   # Bottom right
        [half_size, 0, half_size * 1.5],    # Top right
        [-half_size, 0, half_size * 1.5]    # Top left
    ]
    
    for vert in leaf_verts:
        # Transform to world space
        world_pos = position + vert[0] * right + vert[1] * up + vert[2] * forward
        vertices.append(world_pos)
        normals.append(up)
    
    # Texture coordinates
    texcoords = [
        [0, 0], [1, 0], [1, 1], [0, 1]
    ]
    
    # Indices for two triangles
    indices = [0, 1, 2, 0, 2, 3]
    
    return np.array(vertices), np.array(normals), np.array(texcoords), np.array(indices)

def generate_sphere(radius, segments=16):
    """Generate sphere geometry for debugging or special effects"""
    vertices = []
    normals = []
    texcoords = []
    indices = []
    
    for lat in range(segments + 1):
        theta = math.pi * lat / segments
        sin_theta = math.sin(theta)
        cos_theta = math.cos(theta)
        
        for lon in range(segments + 1):
            phi = 2 * math.pi * lon / segments
            sin_phi = math.sin(phi)
            cos_phi = math.cos(phi)
            
            x = radius * sin_theta * cos_phi
            y = radius * cos_theta
            z = radius * sin_theta * sin_phi
            
            vertices.append([x, y, z])
            normals.append([x/radius, y/radius, z/radius])
            texcoords.append([lon/segments, lat/segments])
    
    # Generate indices
    for lat in range(segments):
        for lon in range(segments):
            first = lat * (segments + 1) + lon
            second = first + segments + 1
            
            indices.extend([first, second, first + 1])
            indices.extend([second, second + 1, first + 1])
    
    return np.array(vertices), np.array(normals), np.array(texcoords), np.array(indices)
