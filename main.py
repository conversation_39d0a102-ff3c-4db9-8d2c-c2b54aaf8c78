"""
3D Plant Growth Simulation
A realistic plant growth simulator using L-Systems and 3D rendering
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys

from plant_system.plant import Plant
from rendering.renderer import PlantRenderer
from rendering.camera import Camera

class PlantSimulation:
    """Main application class for plant growth simulation"""
    
    def __init__(self, width=1200, height=800):
        self.width = width
        self.height = height
        self.running = True
        
        # Initialize Pygame
        pygame.init()
        pygame.display.set_mode((width, height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption("3D Plant Growth Simulation")
        
        # Create ModernGL context
        self.ctx = mgl.create_context()
        
        # Initialize renderer
        self.renderer = PlantRenderer(width, height)
        self.renderer.initialize(self.ctx)
        
        # Create plant
        self.plant = Plant(plant_type='realistic_tree', position=[0, 0, 0])
        
        # Input state
        self.keys = {}
        self.mouse_pos = pygame.mouse.get_pos()
        self.mouse_buttons = [False, False, False]
        
        # Simulation controls
        self.paused = False
        self.growth_speed = 1.0
        self.show_stats = True
        
        # Timing
        self.clock = pygame.time.Clock()
        self.dt = 0.0
        self.fps_target = 60
        
        # UI font
        pygame.font.init()
        self.font = pygame.font.Font(None, 24)
        
        print("Plant simulation initialized")
        print("Controls:")
        print("  Mouse: Rotate camera")
        print("  Mouse wheel: Zoom")
        print("  WASD: Pan camera")
        print("  Space: Pause/Resume")
        print("  R: Reset plant")
        print("  +/-: Adjust growth speed")
        print("  F: Toggle wireframe")
        print("  H: Toggle help")
        print("  ESC: Exit")
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                self.keys[event.key] = True
                
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    print(f"Simulation {'paused' if self.paused else 'resumed'}")
                elif event.key == pygame.K_r:
                    self.plant.reset()
                    print("Plant reset")
                elif event.key == pygame.K_f:
                    self.renderer.wireframe_mode = not self.renderer.wireframe_mode
                    print(f"Wireframe mode: {self.renderer.wireframe_mode}")
                elif event.key == pygame.K_h:
                    self.show_stats = not self.show_stats
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.growth_speed = min(5.0, self.growth_speed * 1.2)
                    self.plant.set_growth_speed(self.growth_speed)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
                elif event.key == pygame.K_MINUS:
                    self.growth_speed = max(0.1, self.growth_speed / 1.2)
                    self.plant.set_growth_speed(self.growth_speed)
                    print(f"Growth speed: {self.growth_speed:.1f}x")
            
            elif event.type == pygame.KEYUP:
                self.keys[event.key] = False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.mouse_buttons[event.button - 1] = True
            
            elif event.type == pygame.MOUSEBUTTONUP:
                self.mouse_buttons[event.button - 1] = False
            
            elif event.type == pygame.MOUSEWHEEL:
                # Zoom camera
                self.renderer.camera.zoom(event.y * -0.5)
            
            elif event.type == pygame.MOUSEMOTION:
                if self.mouse_buttons[0]:  # Left mouse button
                    # Rotate camera
                    dx = event.rel[0]
                    dy = event.rel[1]
                    self.renderer.camera.rotate(dx, dy)
                elif self.mouse_buttons[2]:  # Right mouse button
                    # Pan camera
                    dx = event.rel[0]
                    dy = event.rel[1]
                    self.renderer.camera.pan(-dx, dy)
            
            elif event.type == pygame.VIDEORESIZE:
                self.width = event.w
                self.height = event.h
                self.renderer.resize(self.width, self.height)
    
    def handle_continuous_input(self):
        """Handle continuous input (held keys)"""
        # Camera panning with WASD
        pan_speed = 50.0 * self.dt
        if self.keys.get(pygame.K_w, False):
            self.renderer.camera.pan(0, pan_speed)
        if self.keys.get(pygame.K_s, False):
            self.renderer.camera.pan(0, -pan_speed)
        if self.keys.get(pygame.K_a, False):
            self.renderer.camera.pan(-pan_speed, 0)
        if self.keys.get(pygame.K_d, False):
            self.renderer.camera.pan(pan_speed, 0)
        
        # Light direction control
        if self.keys.get(pygame.K_LEFT, False):
            # Rotate light left
            current_light = self.renderer.lighting.get_primary_light()
            if current_light and hasattr(current_light, 'direction'):
                angle = 0.5 * self.dt
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                x, y, z = current_light.direction
                new_x = x * cos_a - z * sin_a
                new_z = x * sin_a + z * cos_a
                current_light.set_direction([new_x, y, new_z])
                self.plant.set_light_direction([new_x, y, new_z])
        
        if self.keys.get(pygame.K_RIGHT, False):
            # Rotate light right
            current_light = self.renderer.lighting.get_primary_light()
            if current_light and hasattr(current_light, 'direction'):
                angle = -0.5 * self.dt
                cos_a, sin_a = np.cos(angle), np.sin(angle)
                x, y, z = current_light.direction
                new_x = x * cos_a - z * sin_a
                new_z = x * sin_a + z * cos_a
                current_light.set_direction([new_x, y, new_z])
                self.plant.set_light_direction([new_x, y, new_z])
    
    def update(self):
        """Update simulation"""
        if not self.paused:
            # Update plant growth
            self.plant.update(self.dt * self.growth_speed)
    
    def render(self):
        """Render the scene"""
        # Render 3D scene
        self.renderer.render(self.plant, self.dt)
        
        # Render UI overlay
        if self.show_stats:
            self.render_ui()
        
        # Swap buffers
        pygame.display.flip()
    
    def render_ui(self):
        """Render UI overlay with statistics"""
        # Get plant statistics
        stats = self.plant.get_stats()
        
        # Create UI text
        ui_lines = [
            f"Age: {stats['age']:.1f}s",
            f"Growth Iteration: {stats['iteration']}/{self.plant.max_iterations}",
            f"Branches: {stats['branches']}",
            f"Leaf Clusters: {stats['leaf_clusters']}",
            f"Total Leaves: {stats['total_leaves']}",
            f"Growing Branches: {stats['growing_branches']}",
            f"Growth Speed: {self.growth_speed:.1f}x",
            f"FPS: {self.clock.get_fps():.1f}",
            "",
            "Controls:",
            "Mouse: Rotate camera",
            "Mouse wheel: Zoom",
            "WASD: Pan camera",
            "Arrow keys: Move light",
            "Space: Pause/Resume",
            "R: Reset plant",
            "+/-: Growth speed",
            "F: Wireframe",
            "H: Toggle help",
            "ESC: Exit"
        ]
        
        # Render text to surface (this is a simplified approach)
        # In a real application, you'd want to use a proper UI library
        y_offset = 10
        for line in ui_lines:
            if line:  # Skip empty lines
                # Note: This is a placeholder for UI rendering
                # In practice, you'd need to render text to a texture
                # and display it as an overlay
                pass
            y_offset += 20
    
    def run(self):
        """Main application loop"""
        print("Starting plant growth simulation...")
        
        while self.running:
            # Calculate delta time
            self.dt = self.clock.tick(self.fps_target) / 1000.0
            
            # Handle events
            self.handle_events()
            self.handle_continuous_input()
            
            # Update simulation
            self.update()
            
            # Render
            self.render()
        
        # Cleanup
        self.renderer.cleanup()
        pygame.quit()
        print("Simulation ended")

def main():
    """Main entry point"""
    try:
        # Create and run simulation
        simulation = PlantSimulation(1200, 800)
        simulation.run()
    except Exception as e:
        print(f"Error running simulation: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
