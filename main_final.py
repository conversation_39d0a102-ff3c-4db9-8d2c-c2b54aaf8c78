"""
Final Working 3D Plant Growth Simulation
Simplified rendering to ensure compatibility
"""
import pygame
import moderngl as mgl
import numpy as np
import time
import sys
import math

from plant_system.plant import Plant

class FinalPlantSimulation:
    """Final working plant simulation with basic 3D rendering"""
    
    def __init__(self, width=1000, height=700):
        self.width = width
        self.height = height
        self.running = True
        
        print("🌱 Initializing Final Plant Growth Simulation...")
        
        # Initialize Pygame
        pygame.init()
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
        pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
        pygame.display.gl_set_attribute(pygame.GL_DEPTH_SIZE, 24)
        
        self.screen = pygame.display.set_mode((width, height), pygame.OPENGL | pygame.DOUBLEBUF)
        pygame.display.set_caption("🌳 3D Plant Growth Simulation - Final Version")
        
        # Create OpenGL context
        self.ctx = mgl.create_context()
        self.ctx.enable(mgl.DEPTH_TEST)
        
        print(f"✓ OpenGL {self.ctx.info['GL_VERSION']}")
        print(f"✓ GPU: {self.ctx.info['GL_RENDERER']}")
        
        # Create simple shader program
        self.program = self.ctx.program(
            vertex_shader="""
            #version 330 core
            layout (location = 0) in vec3 aPos;
            layout (location = 1) in vec3 aNormal;
            
            uniform mat4 mvp;
            uniform mat4 model;
            uniform mat3 normalMatrix;
            uniform vec3 lightDir;
            
            out float lighting;
            
            void main() {
                vec3 normal = normalize(normalMatrix * aNormal);
                lighting = max(0.3, dot(normal, -lightDir));
                gl_Position = mvp * vec4(aPos, 1.0);
            }
            """,
            fragment_shader="""
            #version 330 core
            in float lighting;
            out vec4 FragColor;
            uniform vec3 color;
            
            void main() {
                FragColor = vec4(color * lighting, 1.0);
            }
            """
        )
        
        # Create plant
        self.plant = Plant(plant_type='realistic_tree', position=[0, 0, 0])
        
        # Camera parameters
        self.camera_distance = 3.0
        self.camera_angle_x = 0.0
        self.camera_angle_y = 0.4
        
        # Control state
        self.keys = {}
        self.mouse_buttons = [False, False, False]
        self.paused = False
        self.growth_speed = 1.0
        self.show_stats = True
        self.clock = pygame.time.Clock()
        
        # Rendering state
        self.wireframe = False
        
        print("✓ Final simulation ready!")
    
    def get_mvp_matrix(self):
        """Calculate model-view-projection matrix"""
        # Camera position
        cam_x = self.camera_distance * math.sin(self.camera_angle_y) * math.cos(self.camera_angle_x)
        cam_y = self.camera_distance * math.cos(self.camera_angle_y)
        cam_z = self.camera_distance * math.sin(self.camera_angle_y) * math.sin(self.camera_angle_x)
        
        # View matrix
        eye = np.array([cam_x, cam_y, cam_z])
        target = np.array([0, 1, 0])
        up = np.array([0, 1, 0])
        
        forward = target - eye
        forward = forward / np.linalg.norm(forward)
        right = np.cross(forward, up)
        right = right / np.linalg.norm(right)
        up = np.cross(right, forward)
        
        view = np.eye(4)
        view[0, :3] = right
        view[1, :3] = up
        view[2, :3] = -forward
        view[:3, 3] = -np.array([np.dot(right, eye), np.dot(up, eye), np.dot(forward, eye)])
        
        # Projection matrix
        fov = math.radians(45)
        aspect = self.width / self.height
        near, far = 0.1, 100.0
        
        f = 1.0 / math.tan(fov / 2.0)
        proj = np.zeros((4, 4))
        proj[0, 0] = f / aspect
        proj[1, 1] = f
        proj[2, 2] = (far + near) / (near - far)
        proj[2, 3] = (2 * far * near) / (near - far)
        proj[3, 2] = -1
        
        # Model matrix (identity)
        model = np.eye(4)
        
        return (proj @ view @ model).astype(np.float32), model.astype(np.float32)
    
    def render_geometry(self, vertices, normals, indices, color):
        """Render geometry with basic lighting"""
        if len(vertices) == 0 or len(indices) == 0:
            return
        
        try:
            # Interleave vertex data
            vertex_data = np.column_stack([vertices, normals]).astype(np.float32)
            
            # Create buffers
            vbo = self.ctx.buffer(vertex_data.tobytes())
            ibo = self.ctx.buffer(indices.astype(np.uint32).tobytes())
            vao = self.ctx.vertex_array(self.program, [(vbo, '3f 3f', 'aPos', 'aNormal')], ibo)
            
            # Set uniforms
            mvp, model = self.get_mvp_matrix()
            normal_matrix = np.linalg.inv(model[:3, :3]).T.astype(np.float32)
            
            self.program['mvp'].write(mvp.tobytes())
            self.program['model'].write(model.tobytes())
            self.program['normalMatrix'].write(normal_matrix.tobytes())
            self.program['lightDir'].value = (0.3, -1.0, 0.2)  # Light direction
            self.program['color'].value = color
            
            # Render
            if self.wireframe:
                self.ctx.wireframe = True
            else:
                self.ctx.wireframe = False
                
            vao.render()
            
            # Cleanup
            vao.release()
            vbo.release()
            ibo.release()
            
        except Exception as e:
            print(f"Render error: {e}")
    
    def render_plant(self):
        """Render the plant with basic geometry"""
        # Render branches
        branch_geom = self.plant.get_branch_geometry()
        if branch_geom[0] is not None:
            vertices, normals, texcoords, indices = branch_geom
            self.render_geometry(vertices, normals, indices, (0.6, 0.4, 0.2))  # Brown
        
        # Render leaves
        leaf_geom = self.plant.get_leaf_geometry()
        if leaf_geom[0] is not None:
            vertices, normals, texcoords, indices = leaf_geom
            self.render_geometry(vertices, normals, indices, (0.2, 0.8, 0.2))  # Green
    
    def handle_events(self):
        """Handle input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                self.keys[event.key] = True
                
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    print(f"{'⏸️ Paused' if self.paused else '▶️ Resumed'}")
                elif event.key == pygame.K_r:
                    self.plant.reset()
                    print("🔄 Plant reset")
                elif event.key == pygame.K_f:
                    self.wireframe = not self.wireframe
                    print(f"🔲 Wireframe: {'ON' if self.wireframe else 'OFF'}")
                elif event.key == pygame.K_h:
                    self.show_stats = not self.show_stats
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.growth_speed = min(5.0, self.growth_speed * 1.2)
                    print(f"⚡ Growth speed: {self.growth_speed:.1f}x")
                elif event.key == pygame.K_MINUS:
                    self.growth_speed = max(0.1, self.growth_speed / 1.2)
                    print(f"🐌 Growth speed: {self.growth_speed:.1f}x")
            
            elif event.type == pygame.KEYUP:
                self.keys[event.key] = False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.mouse_buttons[event.button - 1] = True
            
            elif event.type == pygame.MOUSEBUTTONUP:
                self.mouse_buttons[event.button - 1] = False
            
            elif event.type == pygame.MOUSEWHEEL:
                self.camera_distance = max(1.0, min(10.0, self.camera_distance - event.y * 0.3))
            
            elif event.type == pygame.MOUSEMOTION:
                if self.mouse_buttons[0]:  # Left mouse button
                    self.camera_angle_x += event.rel[0] * 0.01
                    self.camera_angle_y = max(0.1, min(math.pi - 0.1, 
                                                     self.camera_angle_y + event.rel[1] * 0.01))
    
    def run(self):
        """Main application loop"""
        print("\n🚀 Starting 3D Plant Growth Simulation!")
        print("\n🎮 Controls:")
        print("  🖱️  Mouse: Rotate camera")
        print("  🎡 Mouse wheel: Zoom")
        print("  ⏸️  Space: Pause/Resume")
        print("  🔄 R: Reset plant")
        print("  ⚡ +/-: Growth speed")
        print("  🔲 F: Wireframe")
        print("  📊 H: Toggle stats")
        print("  🚪 ESC: Exit")
        print()
        
        last_stats_time = time.time()
        
        try:
            while self.running:
                dt = self.clock.tick(60) / 1000.0
                
                # Handle events
                self.handle_events()
                
                # Update plant
                if not self.paused:
                    self.plant.update(dt * self.growth_speed)
                
                # Render
                self.ctx.clear(0.5, 0.7, 1.0, 1.0)  # Sky blue
                self.render_plant()
                pygame.display.flip()
                
                # Print stats
                if self.show_stats:
                    current_time = time.time()
                    if current_time - last_stats_time >= 5.0:
                        stats = self.plant.get_stats()
                        fps = self.clock.get_fps()
                        print(f"🌱 Age: {stats['age']:.1f}s | 🌿 Branches: {stats['branches']} | "
                              f"🔄 Iteration: {stats['iteration']} | 🍃 Leaves: {stats['total_leaves']} | "
                              f"📈 FPS: {fps:.1f}")
                        last_stats_time = current_time
        
        except KeyboardInterrupt:
            print("\n👋 Simulation stopped by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            pygame.quit()
        
        print("✅ Simulation ended successfully")
        return 0

def main():
    """Main entry point"""
    try:
        simulation = FinalPlantSimulation(1000, 700)
        return simulation.run()
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
